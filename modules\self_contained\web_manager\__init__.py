"""
Web管理后台系统主模块

基于xiaomai-bot项目框架，提供完整的Web管理界面，包括：
- 账号实例管理
- 实时控制台
- 聊天管理界面
- 配置管理
- 安全认证系统
"""

import asyncio
from pathlib import Path

from creart import create
from graia.ariadne import Ariadne
from graia.ariadne.event.lifecycle import ApplicationLaunched
from graia.saya import Channel, Saya
from graia.saya.builtins.broadcast.schema import ListenerSchema
from loguru import logger

from core.config import GlobalConfig
from core.models import saya_model

# 模块元数据
module_controller = saya_model.get_module_controller()
channel = Channel.current()
channel.meta["name"] = "Web管理后台"
channel.meta["description"] = (
    "提供完整的Web管理界面，包括账号管理、实时控制台、聊天管理等功能"
)
channel.meta["author"] = "xiaomai-bot"
channel.metadata = module_controller.get_metadata_from_path(Path(__file__))

# 全局配置
config = create(GlobalConfig)

# 检查Web管理是否启用
if not config.web_manager_api:
    logger.warning("Web管理API未启用，请在配置文件中设置 web_manager_api: true")
else:
    logger.info("正在加载Web管理后台系统...")

    try:
        # 导入API路由
        from fastapi import FastAPI

        # 获取FastAPI实例并配置路由
        from .api import auth, bot, chat
        from .api import config as config_api
        from .static_handler import router as static_router
        from .static_handler import setup_static_routes
        from .websocket.chat import websocket_endpoint as chat_websocket_endpoint
        from .websocket.console import websocket_endpoint

        # 在模块加载时注册路由
        def setup_web_manager():
            """设置Web管理后台路由"""
            try:
                # 这里需要在FastAPI服务启动后注册路由
                # 实际的路由注册会在应用启动时进行
                pass
            except Exception as e:
                logger.error(f"Web管理后台路由设置失败: {e}")

        # 注册启动回调
        @channel.use(ListenerSchema(listening_events=[ApplicationLaunched]))
        async def on_app_launch(app: Ariadne):
            """应用启动时初始化Web管理后台"""
            try:
                # 检查是否启用Web管理API
                config = create(GlobalConfig)
                if not config.web_manager_api:
                    logger.info("Web管理API未启用，跳过初始化")
                    return

                # 直接从Saya获取FastAPI实例
                saya = create(Saya)
                fastapi_app = None

                # 尝试从FastAPIBehaviour获取FastAPI实例
                for behaviour in saya.behaviours:
                    if hasattr(behaviour, "fastapi"):
                        fastapi_app = behaviour.fastapi
                        break

                if fastapi_app:
                    logger.info("找到FastAPI实例，开始注册Web管理路由...")

                    # 初始化认证服务和默认用户
                    from .services.auth_service import AuthService

                    auth_service = AuthService()
                    await auth_service.initialize_default_user()
                    logger.info("认证服务初始化完成")

                    # 设置静态文件服务
                    setup_static_routes(fastapi_app)
                    logger.info("静态文件服务已设置")

                    # 注册静态页面路由
                    fastapi_app.include_router(static_router)
                    logger.info("静态页面路由已注册")

                    # 注册API路由
                    logger.info("正在注册API路由...")
                    fastapi_app.include_router(auth.router)
                    logger.info("认证路由已注册")
                    fastapi_app.include_router(bot.router)
                    logger.info("机器人路由已注册")
                    fastapi_app.include_router(chat.router)
                    logger.info("聊天路由已注册")
                    fastapi_app.include_router(config_api.router)
                    logger.info("配置路由已注册")

                    # 注册WebSocket路由
                    from fastapi import Query, WebSocket

                    @fastapi_app.websocket("/ws/console")
                    async def websocket_console_endpoint_wrapper(
                        websocket: WebSocket, token: str = Query("")
                    ):
                        await websocket_endpoint(websocket, token)

                    @fastapi_app.websocket("/ws/chat")
                    async def websocket_chat_endpoint_wrapper(
                        websocket: WebSocket, token: str = Query("")
                    ):
                        await chat_websocket_endpoint(websocket, token)

                    logger.success("Web管理后台系统初始化完成")
                    logger.info(
                        f"Web管理界面地址: http://{'0.0.0.0' if config.api_expose else '127.0.0.1'}:{config.api_port}/"
                    )

                else:
                    logger.error("未找到FastAPI服务，Web管理后台无法启动")

            except Exception as e:
                logger.error(f"Web管理后台初始化失败: {e}")
                logger.exception(e)

        logger.success("Web管理后台系统加载完成")

    except ImportError as e:
        logger.error(f"Web管理后台模块导入失败: {e}")
        logger.warning("请确保已安装所需依赖: fastapi, jinja2, python-multipart")
    except Exception as e:
        logger.error(f"Web管理后台系统加载失败: {e}")
        logger.exception(e)
