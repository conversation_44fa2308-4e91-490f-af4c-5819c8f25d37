{% extends "base.html" %}

{% block title %}聊天管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-comments"></i> 聊天管理</h2>
                    <p class="text-muted" id="page-subtitle">选择机器人账号进行聊天管理</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="refreshBots()" id="refresh-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="btn btn-secondary" onclick="backToBotList()" id="back-btn" style="display: none;">
                        <i class="fas fa-arrow-left"></i> 返回账号列表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 机器人账号选择界面 -->
    <div id="bot-selection" class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-robot"></i> 选择机器人账号</h5>
                </div>
                <div class="card-body">
                    <div id="bot-list" class="row">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载机器人账号...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 聊天管理界面 -->
    <div id="chat-interface" style="display: none;">
        <!-- 聊天统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title">群组数量</h6>
                        <h3 class="text-primary" id="groups-count">-</h3>
                        <small class="text-muted">个群组</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title">好友数量</h6>
                        <h3 class="text-success" id="friends-count">-</h3>
                        <small class="text-muted">位好友</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title">在线状态</h6>
                        <h3 class="text-info" id="bot-status">-</h3>
                        <small class="text-muted">连接状态</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h6 class="card-title">账号昵称</h6>
                        <h3 class="text-warning" id="bot-nickname">-</h3>
                        <small class="text-muted">机器人昵称</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 聊天列表和详情 -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> 聊天列表</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary active" id="groups-tab" onclick="switchChatType('groups')">
                                <i class="fas fa-users"></i> 群组
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="friends-tab" onclick="switchChatType('friends')">
                                <i class="fas fa-user"></i> 好友
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="chat-list" class="list-group list-group-flush">
                            <div class="text-center p-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载聊天列表...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-comment-dots"></i> 聊天详情</h5>
                    </div>
                    <div class="card-body">
                        <div id="chat-detail">
                            <div class="text-center text-muted">
                                <i class="fas fa-comments fa-3x mb-3"></i>
                                <p>请从左侧选择一个聊天查看详情</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentBotAccount = null;
let currentChatType = 'groups'; // 'groups' or 'friends'
let currentChatId = null;
let chatWebSocket = null;
let wsReconnectAttempts = 0;
const maxReconnectAttempts = 5;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载，确保认证头部已设置
    setTimeout(() => {
        loadBotList();
        initWebSocket();
    }, 100);
});

// 初始化WebSocket连接
function initWebSocket() {
    try {
        const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
        if (!token) {
            console.warn('未找到认证Token，无法建立WebSocket连接');
            return;
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/chat?token=${encodeURIComponent(token)}`;

        chatWebSocket = new WebSocket(wsUrl);

        chatWebSocket.onopen = function(event) {
            console.log('聊天WebSocket连接已建立');
            wsReconnectAttempts = 0;
        };

        chatWebSocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        chatWebSocket.onclose = function(event) {
            console.log('聊天WebSocket连接已关闭');
            // 尝试重连
            if (wsReconnectAttempts < maxReconnectAttempts) {
                wsReconnectAttempts++;
                console.log(`尝试重连WebSocket (${wsReconnectAttempts}/${maxReconnectAttempts})`);
                setTimeout(initWebSocket, 3000 * wsReconnectAttempts);
            }
        };

        chatWebSocket.onerror = function(error) {
            console.error('聊天WebSocket错误:', error);
        };

    } catch (error) {
        console.error('初始化WebSocket失败:', error);
    }
}

// 处理WebSocket消息
function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'connected':
            console.log('WebSocket连接成功:', data.message);
            break;
        case 'new_message':
            handleNewMessage(data);
            break;
        case 'subscribed':
            console.log('已订阅群组:', data.group_id);
            break;
        case 'unsubscribed':
            console.log('已取消订阅群组:', data.group_id);
            break;
        case 'error':
            console.error('WebSocket错误:', data.message);
            break;
        default:
            console.log('未知WebSocket消息类型:', data.type);
    }
}

// 处理新消息
function handleNewMessage(data) {
    // 只有当前正在查看的群组才显示新消息
    if (currentChatType === 'groups' && currentChatId === data.group_id) {
        const messageList = document.getElementById('messageList');
        if (messageList) {
            // 添加新消息到消息列表
            const messageHtml = renderSingleMessage(data.message);
            messageList.insertAdjacentHTML('beforeend', messageHtml);

            // 滚动到底部
            scrollToBottom();
        }
    }
}

// 订阅群组消息
function subscribeToGroup(groupId) {
    if (chatWebSocket && chatWebSocket.readyState === WebSocket.OPEN) {
        chatWebSocket.send(JSON.stringify({
            type: 'subscribe_group',
            group_id: groupId
        }));
    }
}

// 取消订阅群组消息
function unsubscribeFromGroup(groupId) {
    if (chatWebSocket && chatWebSocket.readyState === WebSocket.OPEN) {
        chatWebSocket.send(JSON.stringify({
            type: 'unsubscribe_group',
            group_id: groupId
        }));
    }
}

// 加载机器人账号列表
async function loadBotList() {
    try {
        const response = await fetch('/api/v1/chat/bots', {
            headers: window.defaultHeaders
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const bots = await response.json();
        displayBotList(bots);
    } catch (error) {
        console.error('加载机器人列表失败:', error);
        showBotListError();
    }
}

// 显示机器人账号列表
function displayBotList(bots) {
    const botList = document.getElementById('bot-list');
    
    if (!bots || bots.length === 0) {
        botList.innerHTML = `
            <div class="col-12 text-center">
                <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                <p>暂无可用的机器人账号</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    bots.forEach(bot => {
        const statusClass = bot.status === 'online' ? 'success' : 'secondary';
        const statusText = bot.status === 'online' ? '在线' : '离线';
        
        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100 bot-card" onclick="selectBot(${bot.account})">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            ${bot.avatar ?
                                `<img src="${bot.avatar}" class="rounded-circle mx-auto d-block" style="width: 60px; height: 60px; object-fit: cover;" alt="头像" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                 <div class="avatar bg-primary text-white rounded-circle mx-auto d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 24px; display: none;">
                                     ${bot.nickname ? bot.nickname.charAt(0) : bot.account.toString().charAt(0)}
                                 </div>` :
                                `<div class="avatar bg-primary text-white rounded-circle mx-auto d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; font-size: 24px;">
                                     ${bot.nickname ? bot.nickname.charAt(0) : bot.account.toString().charAt(0)}
                                 </div>`
                            }
                        </div>
                        <h5 class="card-title">${bot.nickname || '未知昵称'}</h5>
                        <p class="card-text text-muted">${bot.account}</p>
                        <span class="badge bg-${statusClass}">${statusText}</span>
                        <div class="mt-2">
                            <small class="text-muted">群组: ${bot.groups_count || 0} | 好友: ${bot.friends_count || 0}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    botList.innerHTML = html;
}

// 显示机器人列表加载错误
function showBotListError() {
    const botList = document.getElementById('bot-list');
    botList.innerHTML = `
        <div class="col-12 text-center">
            <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
            <p>加载机器人账号列表失败</p>
            <button class="btn btn-outline-primary" onclick="loadBotList()">重试</button>
        </div>
    `;
}

// 选择机器人账号
function selectBot(account) {
    currentBotAccount = account;
    
    // 隐藏账号选择界面，显示聊天界面
    document.getElementById('bot-selection').style.display = 'none';
    document.getElementById('chat-interface').style.display = 'block';
    
    // 更新按钮状态
    document.getElementById('refresh-btn').style.display = 'none';
    document.getElementById('back-btn').style.display = 'inline-block';
    
    // 更新页面标题
    document.getElementById('page-subtitle').textContent = `管理机器人账号 ${account} 的聊天`;
    
    // 加载聊天数据
    loadBotInfo(account);
    loadChatList();
}

// 返回机器人列表
function backToBotList() {
    currentBotAccount = null;
    currentChatId = null;
    
    // 显示账号选择界面，隐藏聊天界面
    document.getElementById('bot-selection').style.display = 'block';
    document.getElementById('chat-interface').style.display = 'none';
    
    // 更新按钮状态
    document.getElementById('refresh-btn').style.display = 'inline-block';
    document.getElementById('back-btn').style.display = 'none';
    
    // 恢复页面标题
    document.getElementById('page-subtitle').textContent = '选择机器人账号进行聊天管理';
}

// 刷新机器人列表
function refreshBots() {
    loadBotList();
}

// 加载机器人信息
async function loadBotInfo(account) {
    try {
        // 这里可以调用API获取机器人详细信息
        // 暂时使用模拟数据
        document.getElementById('groups-count').textContent = '-';
        document.getElementById('friends-count').textContent = '-';
        document.getElementById('bot-status').textContent = '在线';
        document.getElementById('bot-nickname').textContent = account;
    } catch (error) {
        console.error('加载机器人信息失败:', error);
    }
}

// 切换聊天类型（群组/好友）
function switchChatType(type) {
    currentChatType = type;

    // 更新按钮状态
    document.getElementById('groups-tab').classList.toggle('active', type === 'groups');
    document.getElementById('friends-tab').classList.toggle('active', type === 'friends');

    // 重新加载聊天列表
    loadChatList();

    // 清空聊天详情
    clearChatDetail();
}

// 加载聊天列表
async function loadChatList() {
    if (!currentBotAccount) return;

    const chatList = document.getElementById('chat-list');
    chatList.innerHTML = `
        <div class="text-center p-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载${currentChatType === 'groups' ? '群组' : '好友'}列表...</p>
        </div>
    `;

    try {
        const endpoint = currentChatType === 'groups' ? 'groups' : 'friends';
        const response = await fetch(`/api/v1/chat/${currentBotAccount}/${endpoint}`, {
            headers: window.defaultHeaders
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        displayChatList(data);

        // 更新统计信息
        if (currentChatType === 'groups') {
            document.getElementById('groups-count').textContent = data.length;
        } else {
            document.getElementById('friends-count').textContent = data.length;
        }
    } catch (error) {
        console.error('加载聊天列表失败:', error);
        showChatListError();
    }
}

// 显示聊天列表
function displayChatList(data) {
    const chatList = document.getElementById('chat-list');

    if (!data || data.length === 0) {
        chatList.innerHTML = `
            <div class="text-center p-3">
                <i class="fas fa-${currentChatType === 'groups' ? 'users' : 'user'} fa-2x text-muted mb-3"></i>
                <p>暂无${currentChatType === 'groups' ? '群组' : '好友'}</p>
            </div>
        `;
        return;
    }

    let html = '';
    data.forEach(item => {
        const isGroup = currentChatType === 'groups';
        const name = isGroup ? item.name : (item.remark || item.nickname);
        const subtitle = isGroup ? `${item.member_count} 人` : item.nickname;
        const permission = isGroup ? item.permission : '';

        html += `
            <div class="list-group-item list-group-item-action chat-item" onclick="selectChat('${item.id}', '${currentChatType}')">
                <div class="d-flex w-100 justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="avatar-container me-3" style="width: 40px; height: 40px;">
                            ${item.avatar ?
                                (item.avatar.startsWith('data:') ?
                                    `<img src="${item.avatar}" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;" alt="头像">` :
                                    `<img src="${item.avatar}" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;" alt="头像" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                     <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; display: none;">
                                         ${name ? name.charAt(0) : '?'}
                                     </div>`
                                ) :
                                `<div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                     ${name ? name.charAt(0) : '?'}
                                 </div>`
                            }
                        </div>
                        <div>
                            <h6 class="mb-1">${name || '未知名称'}</h6>
                            <small class="text-muted">${subtitle}</small>
                            ${permission ? `<span class="badge bg-info ms-2">${permission}</span>` : ''}
                        </div>
                    </div>
                    <small class="text-muted">${item.id}</small>
                </div>
            </div>
        `;
    });

    chatList.innerHTML = html;
}

// 显示聊天列表错误
function showChatListError() {
    const chatList = document.getElementById('chat-list');
    chatList.innerHTML = `
        <div class="text-center p-3">
            <i class="fas fa-exclamation-triangle text-warning fa-2x mb-3"></i>
            <p>加载失败</p>
            <button class="btn btn-outline-primary btn-sm" onclick="loadChatList()">重试</button>
        </div>
    `;
}

// 选择聊天
function selectChat(chatId, chatType) {
    currentChatId = chatId;

    // 更新选中状态
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });
    event.currentTarget.classList.add('active');

    // 加载聊天详情
    loadChatDetail(chatId, chatType);
}

// 加载聊天详情
async function loadChatDetail(chatId, chatType) {
    const detail = document.getElementById('chat-detail');
    detail.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载聊天详情...</p>
        </div>
    `;

    try {
        // 获取聊天消息
        const response = await fetch(`/api/v1/chat/${currentBotAccount}/${chatType}/${chatId}/messages`, {
            headers: window.defaultHeaders
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const chatData = await response.json();

        // 渲染聊天界面
        detail.innerHTML = `
            <div class="d-flex flex-column h-100">
                <!-- 聊天头部 -->
                <div class="border-bottom p-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-${chatType === 'groups' ? 'users' : 'user'} me-2"></i>
                        <div>
                            <h5 class="mb-0">${chatData.chat_info.name}</h5>
                            <small class="text-muted">${chatType === 'groups' ? '群组' : '好友'} ${chatId}</small>
                        </div>
                    </div>
                </div>

                <!-- 消息列表 -->
                <div class="flex-grow-1 p-3" style="overflow-y: auto; max-height: 400px;" id="messageList">
                    ${renderMessages(chatData.messages)}
                </div>

                <!-- 消息输入框 -->
                <div class="border-top p-3">
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput" placeholder="输入消息..." onkeypress="handleMessageKeyPress(event, '${chatType}', '${chatId}')">
                        <button class="btn btn-primary" type="button" onclick="sendMessage('${chatType}', '${chatId}')">
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 滚动到底部
        scrollToBottom();

        // 如果是群组聊天，订阅实时消息
        if (chatType === 'groups') {
            // 先取消之前的订阅
            if (currentChatId && currentChatId !== chatId) {
                unsubscribeFromGroup(currentChatId);
            }
            // 订阅新群组
            subscribeToGroup(chatId);
        }

    } catch (error) {
        console.error('加载聊天详情失败:', error);
        showChatDetailError();
    }
}

// 清空聊天详情
function clearChatDetail() {
    const detail = document.getElementById('chat-detail');
    detail.innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-comments fa-3x mb-3"></i>
            <p>请从左侧选择一个聊天查看详情</p>
        </div>
    `;
    currentChatId = null;
}

// 显示聊天详情错误
function showChatDetailError() {
    const detail = document.getElementById('chat-detail');
    detail.innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
            <p>加载聊天详情失败</p>
            <button class="btn btn-outline-primary" onclick="loadChatDetail(currentChatId, currentChatType)">重试</button>
        </div>
    `;
}

// 渲染消息列表
function renderMessages(messages) {
    if (!messages || messages.length === 0) {
        return '<div class="text-center text-muted"><p>暂无消息</p></div>';
    }

    return messages.map(msg => renderSingleMessage(msg)).join('');
}

// 渲染单条消息
function renderSingleMessage(msg) {
    return `
        <div class="mb-3">
            <div class="d-flex align-items-start">
                <div class="avatar-container me-2" style="width: 32px; height: 32px;">
                    ${msg.sender_avatar ?
                        `<img src="${msg.sender_avatar}" class="rounded-circle" style="width: 32px; height: 32px; object-fit: cover;" alt="头像" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 12px; display: none;">
                             ${msg.sender_name.charAt(0)}
                         </div>` :
                        `<div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 12px;">
                             ${msg.sender_name.charAt(0)}
                         </div>`
                    }
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        <strong class="me-2">${msg.sender_name}</strong>
                        <small class="text-muted">${msg.timestamp}</small>
                    </div>
                    <div class="bg-light p-2 rounded">
                        ${msg.content}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 滚动到底部
function scrollToBottom() {
    setTimeout(() => {
        const messageList = document.getElementById('messageList');
        if (messageList) {
            messageList.scrollTop = messageList.scrollHeight;
        }
    }, 100);
}

// 处理键盘事件
function handleMessageKeyPress(event, chatType, chatId) {
    if (event.key === 'Enter') {
        sendMessage(chatType, chatId);
    }
}

// 发送消息
async function sendMessage(chatType, chatId) {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();

    if (!message) {
        return;
    }

    try {
        // 禁用输入框
        input.disabled = true;

        const response = await fetch(`/api/v1/chat/${currentBotAccount}/${chatType}/${chatId}/send`, {
            method: 'POST',
            headers: window.defaultHeaders,
            body: JSON.stringify({ message: message })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // 清空输入框
            input.value = '';

            // 添加消息到界面
            addMessageToUI('小埋', message, new Date().toLocaleString());

            // 显示成功提示
            showToast('消息发送成功', 'success');
        } else {
            throw new Error(result.message || '发送失败');
        }

    } catch (error) {
        console.error('发送消息失败:', error);
        showToast('发送消息失败: ' + error.message, 'error');
    } finally {
        // 重新启用输入框
        input.disabled = false;
        input.focus();
    }
}

// 添加消息到UI
function addMessageToUI(senderName, content, timestamp) {
    const messageList = document.getElementById('messageList');
    if (!messageList) return;

    const messageHtml = `
        <div class="mb-3">
            <div class="d-flex align-items-start">
                <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 12px;">
                    ${senderName.charAt(0)}
                </div>
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        <strong class="me-2">${senderName}</strong>
                        <small class="text-muted">${timestamp}</small>
                    </div>
                    <div class="bg-light p-2 rounded">
                        ${content}
                    </div>
                </div>
            </div>
        </div>
    `;

    messageList.insertAdjacentHTML('beforeend', messageHtml);
    scrollToBottom();
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>

<style>
.bot-card {
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.bot-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.avatar {
    font-weight: bold;
}

.chat-messages {
    max-height: 500px;
    overflow-y: auto;
}

.message-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.message-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
