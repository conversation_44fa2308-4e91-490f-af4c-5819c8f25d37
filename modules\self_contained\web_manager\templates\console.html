{% extends "base.html" %}

{% block title %}实时控制台{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-terminal"></i> 实时控制台</h2>
                    <p class="text-muted">实时查看机器人运行日志和系统信息</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary" onclick="clearConsole()">
                        <i class="fas fa-trash"></i> 清空
                    </button>
                    <button class="btn btn-primary" onclick="toggleAutoScroll()">
                        <i class="fas fa-arrow-down"></i> <span id="auto-scroll-text">自动滚动</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 连接状态 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info" id="connection-status">
                <i class="fas fa-circle-notch fa-spin"></i> 正在连接到控制台...
            </div>
        </div>
    </div>

    <!-- 控制台输出 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-alt"></i> 控制台日志
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="console-output" class="console-output">
                        <!-- 日志内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.console-output {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    height: 600px;
    overflow-y: auto;
    padding: 15px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.console-output::-webkit-scrollbar {
    width: 8px;
}

.console-output::-webkit-scrollbar-track {
    background: #2d2d2d;
}

.console-output::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.console-output::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.log-entry {
    margin-bottom: 2px;
    padding: 2px 0;
}

.log-timestamp {
    color: #569cd6;
}

.log-level-info {
    color: #4ec9b0;
}

.log-level-warning {
    color: #dcdcaa;
}

.log-level-error {
    color: #f44747;
}

.log-level-debug {
    color: #9cdcfe;
}

.log-message {
    color: #d4d4d4;
}
</style>

<script>
let websocket = null;
let autoScroll = true;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

// 页面加载时连接WebSocket
document.addEventListener('DOMContentLoaded', function() {
    connectWebSocket();
});

// 页面卸载时关闭WebSocket
window.addEventListener('beforeunload', function() {
    if (websocket) {
        websocket.close();
    }
});

function connectWebSocket() {
    try {
        const token = getToken();
        if (!token) {
            showConnectionError('未找到认证令牌');
            return;
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/console?token=${token}`;
        
        websocket = new WebSocket(wsUrl);
        
        websocket.onopen = function(event) {
            console.log('WebSocket连接已建立');
            showConnectionSuccess();
            reconnectAttempts = 0;
        };
        
        websocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                appendLogEntry(data);
            } catch (error) {
                // 如果不是JSON，直接显示文本
                appendLogEntry({
                    timestamp: new Date().toISOString(),
                    level: 'info',
                    message: event.data
                });
            }
        };
        
        websocket.onclose = function(event) {
            console.log('WebSocket连接已关闭', event);
            showConnectionClosed();
            
            // 尝试重连
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                setTimeout(() => {
                    console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                    connectWebSocket();
                }, 3000);
            }
        };
        
        websocket.onerror = function(error) {
            console.error('WebSocket错误:', error);
            showConnectionError('连接错误');
        };
        
    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        showConnectionError(error.message);
    }
}

function appendLogEntry(logData) {
    const output = document.getElementById('console-output');
    const entry = document.createElement('div');
    entry.className = 'log-entry';
    
    const timestamp = new Date(logData.timestamp).toLocaleTimeString();
    const level = logData.level || 'info';
    const message = logData.message || '';
    
    entry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> <span class="log-level-${level}">[${level.toUpperCase()}]</span> <span class="log-message">${escapeHtml(message)}</span>`;
    
    output.appendChild(entry);
    
    // 自动滚动到底部
    if (autoScroll) {
        output.scrollTop = output.scrollHeight;
    }
    
    // 限制日志条数，避免内存占用过多
    const maxEntries = 1000;
    while (output.children.length > maxEntries) {
        output.removeChild(output.firstChild);
    }
}

function clearConsole() {
    document.getElementById('console-output').innerHTML = '';
}

function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const button = document.getElementById('auto-scroll-text');
    button.textContent = autoScroll ? '自动滚动' : '手动滚动';
}

function showConnectionSuccess() {
    const status = document.getElementById('connection-status');
    status.className = 'alert alert-success';
    status.innerHTML = '<i class="fas fa-check-circle"></i> 已连接到控制台';
}

function showConnectionClosed() {
    const status = document.getElementById('connection-status');
    status.className = 'alert alert-warning';
    status.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 连接已断开，正在尝试重连...';
}

function showConnectionError(message) {
    const status = document.getElementById('connection-status');
    status.className = 'alert alert-danger';
    status.innerHTML = `<i class="fas fa-times-circle"></i> 连接失败: ${message}`;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function getToken() {
    // 优先从localStorage获取，如果没有则从Cookie获取
    return localStorage.getItem('access_token') || getCookie('access_token');
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}
</script>
{% endblock %}
