"""
聊天WebSocket处理模块

提供实时聊天功能的WebSocket连接管理
"""

import json
import asyncio
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger

from ..utils.security import SecurityManager
from ..services.auth_service import AuthService


class ChatWebSocket:
    """聊天WebSocket管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 用户会话映射
        self.user_sessions: Dict[str, str] = {}
        # 群组订阅
        self.group_subscriptions: Dict[str, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, token: str = None):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            
            if not token:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "需要提供认证Token"
                }))
                await websocket.close()
                return
                
            # 验证Token
            auth_service = AuthService()
            user_info = await auth_service.verify_session(token)
            
            if not user_info:
                await websocket.send_text(json.dumps({
                    "type": "error", 
                    "message": "Token无效或已过期"
                }))
                await websocket.close()
                return
                
            # 生成连接ID
            connection_id = SecurityManager.generate_random_string(16)
            
            # 存储连接
            self.active_connections[connection_id] = websocket
            self.user_sessions[connection_id] = user_info["username"]
            
            # 发送连接成功消息
            await websocket.send_text(json.dumps({
                "type": "connected",
                "connection_id": connection_id,
                "user": user_info["username"],
                "message": "聊天WebSocket连接成功"
            }))
            
            logger.info(f"用户 {user_info['username']} 建立聊天WebSocket连接: {connection_id}")
            
            # 处理消息
            await self._handle_messages(websocket, connection_id)
            
        except WebSocketDisconnect:
            logger.info("聊天WebSocket连接断开")
        except Exception as e:
            logger.exception(f"聊天WebSocket连接失败: {e}")
            try:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"连接失败: {str(e)}"
                }))
                await websocket.close()
            except:
                pass
                
    async def _handle_messages(self, websocket: WebSocket, connection_id: str):
        """处理WebSocket消息"""
        try:
            while True:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                message_type = message.get("type")
                
                if message_type == "subscribe_group":
                    # 订阅群组消息
                    await self._handle_group_subscribe(connection_id, message)
                elif message_type == "unsubscribe_group":
                    # 取消订阅群组
                    await self._handle_group_unsubscribe(connection_id, message)
                elif message_type == "send_message":
                    # 发送消息
                    await self._handle_send_message(connection_id, message)
                elif message_type == "ping":
                    # 心跳检测
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": f"未知消息类型: {message_type}"
                    }))
                    
        except WebSocketDisconnect:
            await self._disconnect(connection_id)
        except Exception as e:
            logger.exception(f"处理聊天WebSocket消息失败: {e}")
            await self._disconnect(connection_id)
            
    async def _handle_group_subscribe(self, connection_id: str, message: Dict[str, Any]):
        """处理群组订阅"""
        try:
            group_id = str(message.get("group_id", ""))
            if not group_id:
                await self._send_to_connection(connection_id, {
                    "type": "error",
                    "message": "群组ID不能为空"
                })
                return
                
            # 添加到群组订阅
            if group_id not in self.group_subscriptions:
                self.group_subscriptions[group_id] = set()
            self.group_subscriptions[group_id].add(connection_id)
            
            await self._send_to_connection(connection_id, {
                "type": "subscribed",
                "group_id": group_id,
                "message": f"已订阅群组 {group_id} 的消息"
            })
            
            logger.debug(f"连接 {connection_id} 订阅群组 {group_id}")
            
        except Exception as e:
            logger.exception(f"处理群组订阅失败: {e}")
            
    async def _handle_group_unsubscribe(self, connection_id: str, message: Dict[str, Any]):
        """处理取消群组订阅"""
        try:
            group_id = str(message.get("group_id", ""))
            if not group_id:
                return
                
            # 从群组订阅中移除
            if group_id in self.group_subscriptions:
                self.group_subscriptions[group_id].discard(connection_id)
                if not self.group_subscriptions[group_id]:
                    del self.group_subscriptions[group_id]
                    
            await self._send_to_connection(connection_id, {
                "type": "unsubscribed", 
                "group_id": group_id,
                "message": f"已取消订阅群组 {group_id}"
            })
            
            logger.debug(f"连接 {connection_id} 取消订阅群组 {group_id}")
            
        except Exception as e:
            logger.exception(f"处理取消群组订阅失败: {e}")
            
    async def _handle_send_message(self, connection_id: str, message: Dict[str, Any]):
        """处理发送消息"""
        try:
            # 这里可以集成实际的消息发送逻辑
            # 目前只是返回确认消息
            await self._send_to_connection(connection_id, {
                "type": "message_sent",
                "message": "消息发送功能待实现"
            })
            
        except Exception as e:
            logger.exception(f"处理发送消息失败: {e}")
            
    async def _send_to_connection(self, connection_id: str, data: Dict[str, Any]):
        """向指定连接发送消息"""
        try:
            if connection_id in self.active_connections:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(data))
        except Exception as e:
            logger.exception(f"向连接 {connection_id} 发送消息失败: {e}")
            await self._disconnect(connection_id)
            
    async def broadcast_to_group(self, group_id: str, data: Dict[str, Any]):
        """向群组广播消息"""
        try:
            group_id = str(group_id)
            if group_id in self.group_subscriptions:
                for connection_id in self.group_subscriptions[group_id].copy():
                    await self._send_to_connection(connection_id, data)
        except Exception as e:
            logger.exception(f"向群组 {group_id} 广播消息失败: {e}")
            
    async def _disconnect(self, connection_id: str):
        """断开连接"""
        try:
            # 从所有群组订阅中移除
            for group_id in list(self.group_subscriptions.keys()):
                self.group_subscriptions[group_id].discard(connection_id)
                if not self.group_subscriptions[group_id]:
                    del self.group_subscriptions[group_id]
                    
            # 移除连接
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            if connection_id in self.user_sessions:
                username = self.user_sessions[connection_id]
                del self.user_sessions[connection_id]
                logger.info(f"用户 {username} 的聊天WebSocket连接已断开: {connection_id}")
                
        except Exception as e:
            logger.exception(f"断开连接失败: {e}")


# 全局聊天WebSocket实例
chat_ws = ChatWebSocket()


async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """聊天WebSocket端点"""
    await chat_ws.connect(websocket, token)
