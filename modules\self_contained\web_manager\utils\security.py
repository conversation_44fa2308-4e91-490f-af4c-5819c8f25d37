"""
安全工具模块

提供密码加密、JWT <PERSON>生成验证、权限检查等安全功能
"""

import secrets
from datetime import datetime, timedelta
from typing import Any

import bcrypt
import jwt
from creart import create

from core.config import GlobalConfig

# 密码加密配置
BCRYPT_ROUNDS = 12

# JWT配置
SECRET_KEY = secrets.token_urlsafe(32)  # 在生产环境中应该从配置文件读取
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

config = create(GlobalConfig)


class SecurityManager:
    """安全管理器"""

    @staticmethod
    def hash_password(password: str) -> str:
        """加密密码"""
        salt = bcrypt.gensalt(rounds=BCRYPT_ROUNDS)
        return bcrypt.hashpw(password.encode("utf-8"), salt).decode("utf-8")

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(
            plain_password.encode("utf-8"), hashed_password.encode("utf-8")
        )

    @staticmethod
    def create_access_token(
        data: dict[str, Any], expires_delta: timedelta | None = None
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(token: str) -> dict[str, Any] | None:
        """验证令牌"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except jwt.PyJWTError:
            return None

    @staticmethod
    def generate_session_id() -> str:
        """生成会话ID"""
        return secrets.token_urlsafe(32)

    @staticmethod
    def is_master(qq_id: int) -> bool:
        """检查是否为Master"""
        return qq_id == config.Master

    @staticmethod
    def check_permission(user_role: str, required_role: str) -> bool:
        """检查权限"""
        role_hierarchy = {"user": 1, "admin": 2, "master": 3}

        user_level = role_hierarchy.get(user_role, 0)
        required_level = role_hierarchy.get(required_role, 0)

        return user_level >= required_level

    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """生成随机密码"""
        import string

        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return "".join(secrets.choice(alphabet) for _ in range(length))

    @staticmethod
    def validate_password_strength(password: str) -> dict[str, Any]:
        """验证密码强度"""
        result = {"valid": True, "score": 0, "issues": []}

        if len(password) < 8:
            result["valid"] = False
            result["issues"].append("密码长度至少8位")
        else:
            result["score"] += 1

        if not any(c.isupper() for c in password):
            result["issues"].append("建议包含大写字母")
        else:
            result["score"] += 1

        if not any(c.islower() for c in password):
            result["issues"].append("建议包含小写字母")
        else:
            result["score"] += 1

        if not any(c.isdigit() for c in password):
            result["issues"].append("建议包含数字")
        else:
            result["score"] += 1

        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            result["issues"].append("建议包含特殊字符")
        else:
            result["score"] += 1

        return result
