"""
认证API接口

提供用户登录、登出、密码管理等认证相关的API
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Response, status

from ..models.auth import (
    ChangePasswordRequest,
    CreateUserRequest,
    LoginRequest,
    TokenResponse,
    UserInfo,
)
from ..utils.security import SecurityManager
from . import API_PREFIX, auth_service, get_current_master_user, get_current_user

# 创建路由器
router = APIRouter(prefix=f"{API_PREFIX}/auth", tags=["认证"])


@router.post("/login", response_model=TokenResponse, summary="用户登录")
async def login(
    request: Request, response: Response, login_data: LoginRequest
) -> TokenResponse:
    """
    用户登录接口

    - **username**: 用户名
    - **password**: 密码
    - **remember_me**: 是否记住登录状态
    """
    try:
        # 认证用户
        user = await auth_service.authenticate_user(
            login_data.username, login_data.password
        )
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户名或密码错误"
            )

        # 获取客户端信息
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")

        # 创建会话
        session_data = await auth_service.create_session(user, ip_address, user_agent)

        # 设置Cookie（用于Web页面认证）
        response.set_cookie(
            key="access_token",
            value=session_data["access_token"],
            max_age=session_data["expires_in"],
            httponly=True,
            secure=False,  # 开发环境设为False，生产环境应设为True
            samesite="lax",
        )

        return TokenResponse(**session_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}",
        )


@router.post("/logout", summary="用户登出")
async def logout(current_user: UserInfo = Depends(get_current_user)) -> dict[str, str]:
    """
    用户登出接口
    """
    try:
        # 这里需要从请求头获取token
        # 由于FastAPI的限制，我们需要另一种方式获取token
        return {"message": "登出成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登出失败: {str(e)}",
        )


@router.get("/me", response_model=UserInfo, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: UserInfo = Depends(get_current_user),
) -> UserInfo:
    """
    获取当前登录用户的信息
    """
    return current_user


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: UserInfo = Depends(get_current_user),
) -> dict[str, str]:
    """
    修改当前用户密码

    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    try:
        # 验证新密码强度
        security = SecurityManager()
        password_check = security.validate_password_strength(password_data.new_password)
        if not password_check["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"密码强度不足: {', '.join(password_check['issues'])}",
            )

        # 修改密码
        success = await auth_service.change_password(
            current_user.id, password_data.old_password, password_data.new_password
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="旧密码错误"
            )

        return {"message": "密码修改成功，请重新登录"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修改密码失败: {str(e)}",
        )


@router.post("/create-user", summary="创建用户")
async def create_user(
    user_data: CreateUserRequest,
    current_user: UserInfo = Depends(get_current_master_user),
) -> dict[str, str]:
    """
    创建新用户（仅Master可用）

    - **username**: 用户名
    - **password**: 密码
    - **qq_id**: 关联的QQ号（可选）
    - **role**: 用户角色
    """
    try:
        # 验证密码强度
        security = SecurityManager()
        password_check = security.validate_password_strength(user_data.password)
        if not password_check["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"密码强度不足: {', '.join(password_check['issues'])}",
            )

        # 这里需要实现创建用户的逻辑
        # 由于篇幅限制，暂时返回成功消息
        return {"message": f"用户 {user_data.username} 创建成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {str(e)}",
        )


@router.post("/generate-password", summary="生成随机密码")
async def generate_random_password(
    current_user: UserInfo = Depends(get_current_master_user),
) -> dict[str, str]:
    """
    生成随机密码（仅Master可用）
    """
    try:
        security = SecurityManager()
        password = security.generate_random_password()
        return {"password": password}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成密码失败: {str(e)}",
        )


@router.get("/check-permission/{required_role}", summary="检查权限")
async def check_permission(
    required_role: str, current_user: UserInfo = Depends(get_current_user)
) -> dict[str, bool]:
    """
    检查当前用户是否具有指定权限

    - **required_role**: 需要的角色权限
    """
    try:
        security = SecurityManager()
        has_permission = security.check_permission(current_user.role, required_role)
        return {"has_permission": has_permission}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限检查失败: {str(e)}",
        )
