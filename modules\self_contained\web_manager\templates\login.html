{% extends "base.html" %}

{% block title %}登录 - 小埋Bot管理后台{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-robot text-primary" style="font-size: 3rem;"></i>
                        <h3 class="mt-2">小埋Bot</h3>
                        <p class="text-muted">Web管理后台</p>
                    </div>
                    
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="bi bi-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                            <label class="form-check-label" for="rememberMe">
                                记住登录状态
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <span class="spinner-border spinner-border-sm d-none" id="loginSpinner"></span>
                                登录
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            首次使用请联系管理员获取账号
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 检查URL参数，如果是从dashboard重定向来的，清除可能无效的token
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('from') === 'dashboard') {
        localStorage.removeItem('access_token');
    }

    // 检查是否已经登录（但不自动跳转，避免循环）
    const token = localStorage.getItem('access_token');
    if (token && !urlParams.get('from')) {
        // 只有在不是从其他页面重定向来的情况下才自动跳转
        window.location.href = '/dashboard';
        return;
    }
    
    // 登录表单处理
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const loginSpinner = document.getElementById('loginSpinner');
    
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(loginForm);
        const loginData = {
            username: formData.get('username'),
            password: formData.get('password'),
            remember_me: formData.get('remember_me') === 'on'
        };
        
        // 显示加载状态
        loginBtn.disabled = true;
        loginSpinner.classList.remove('d-none');
        
        try {
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                // 登录成功
                localStorage.setItem('access_token', result.access_token);
                showToast('登录成功！', 'success');
                
                // 延迟跳转，让用户看到成功消息
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                // 登录失败
                showToast(result.detail || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            showToast('网络错误，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            loginBtn.disabled = false;
            loginSpinner.classList.add('d-none');
        }
    });
});

function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'bi bi-eye';
    }
}

// 简单的Toast通知函数
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    const toastId = 'toast-' + Date.now();
    
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" 
             role="alert" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // 自动清理DOM
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}
</script>
{% endblock %}
