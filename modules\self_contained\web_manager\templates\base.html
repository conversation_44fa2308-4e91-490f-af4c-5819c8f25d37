<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}小埋Bot - Web管理后台{% endblock %}</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    {% if user %}
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-robot"></i> 小埋Bot管理后台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2"></i> 控制台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/bots">
                            <i class="bi bi-cpu"></i> 机器人管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/console">
                            <i class="bi bi-terminal"></i> 实时控制台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/chat">
                            <i class="bi bi-chat-dots"></i> 聊天管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/config">
                            <i class="bi bi-gear"></i> 配置管理
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ user.username }}
                            <span class="badge bg-secondary">{{ user.role }}</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changePassword()">
                                <i class="bi bi-key"></i> 修改密码
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- 主要内容区域 -->
    <main class="{% if user %}container-fluid mt-4{% else %}d-flex align-items-center min-vh-100{% endif %}">
        {% block content %}{% endblock %}
    </main>
    
    <!-- 通知容器 -->
    <div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
    
    <!-- 模态框容器 -->
    <div id="modal-container"></div>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 公共JavaScript -->
    <script src="/static/js/common.js"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <script>
        // 全局配置
        window.API_BASE = '/api/v1';
        window.WS_BASE = 'ws://localhost:8080/ws';
        
        // 获取当前用户信息
        {% if user %}
        window.currentUser = {{ user.json() | safe }};
        {% endif %}
        
        // 通用函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('access_token');
                window.location.href = '/login';
            }
        }
        
        function changePassword() {
            // 这里可以打开修改密码的模态框
            showToast('功能开发中...', 'info');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            const token = localStorage.getItem('access_token');
            if (!token && !window.location.pathname.includes('/login')) {
                window.location.href = '/login';
            }
            
            // 设置API请求的默认头部
            if (token) {
                window.defaultHeaders = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };
            }
        });
    </script>
</body>
</html>
