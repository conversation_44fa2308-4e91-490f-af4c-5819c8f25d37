{% extends "base.html" %}

{% block title %}控制台 - 小埋Bot管理后台{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col">
            <h2><i class="bi bi-speedometer2"></i> 控制台总览</h2>
            <p class="text-muted">实时监控机器人运行状态和系统信息</p>
        </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">在线账号</h6>
                            <h3 id="onlineAccounts">-</h3>
                            <small>总计 <span id="totalAccounts">-</span> 个账号</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-robot" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">活跃群组</h6>
                            <h3 id="totalGroups">-</h3>
                            <small>已加入群组</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">好友数量</h6>
                            <h3 id="totalFriends">-</h3>
                            <small>已添加好友</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-heart" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">今日消息</h6>
                            <h3 id="messagesToday">-</h3>
                            <small>总计 <span id="messagesTotal">-</span> 条</small>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-chat-dots" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统信息和机器人状态 -->
    <div class="row">
        <!-- 系统信息 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-cpu"></i> 系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">CPU使用率</label>
                                <div class="progress">
                                    <div class="progress-bar" id="cpuProgress" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="cpuText">0%</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">内存使用率</label>
                                <div class="progress">
                                    <div class="progress-bar bg-info" id="memoryProgress" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="memoryText">0%</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <strong>运行时间:</strong><br>
                            <span id="uptime">-</span>
                        </div>
                        <div class="col-6">
                            <strong>启动时间:</strong><br>
                            <span id="startTime">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 机器人实例状态 -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-robot"></i> 机器人实例</h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshBotInstances()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="botInstancesList">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let refreshInterval;

document.addEventListener('DOMContentLoaded', function() {
    // 初始加载数据
    loadDashboardData();
    
    // 设置定时刷新
    refreshInterval = setInterval(loadDashboardData, 2000); // 2秒刷新一次
});

async function loadDashboardData() {
    try {
        // 加载统计信息
        await loadStatistics();
        
        // 加载机器人实例
        await loadBotInstances();
        
    } catch (error) {
        console.error('加载数据失败:', error);
        showToast('加载数据失败', 'error');
    }
}

async function loadStatistics() {
    try {
        const response = await fetch('/api/v1/bot/statistics', {
            headers: window.WebManager.defaultHeaders
        });
        
        if (response.ok) {
            const stats = await response.json();
            
            // 更新统计数据
            document.getElementById('onlineAccounts').textContent = stats.online_accounts;
            document.getElementById('totalAccounts').textContent = stats.total_accounts;
            document.getElementById('totalGroups').textContent = stats.total_groups;
            document.getElementById('totalFriends').textContent = stats.total_friends;
            document.getElementById('messagesToday').textContent = stats.messages_today;
            document.getElementById('messagesTotal').textContent = stats.messages_total;
            document.getElementById('uptime').textContent = stats.uptime;
            
            // 更新系统信息
            updateProgressBar('cpuProgress', 'cpuText', stats.cpu_usage);
            updateProgressBar('memoryProgress', 'memoryText', stats.memory_usage);
        }
    } catch (error) {
        console.error('加载统计信息失败:', error);
    }
}

async function loadBotInstances() {
    try {
        const response = await fetch('/api/v1/bot/instances', {
            headers: window.WebManager.defaultHeaders
        });
        
        if (response.ok) {
            const instances = await response.json();
            renderBotInstances(instances);
        }
    } catch (error) {
        console.error('加载机器人实例失败:', error);
    }
}

function renderBotInstances(instances) {
    const container = document.getElementById('botInstancesList');
    
    if (instances.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">暂无机器人实例</p>';
        return;
    }
    
    const html = instances.map(instance => {
        const statusClass = getStatusClass(instance.status);
        const statusText = getStatusText(instance.status);
        
        return `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <strong>${instance.account}</strong>
                    ${instance.nickname ? `<small class="text-muted">(${instance.nickname})</small>` : ''}
                    <br>
                    <small class="text-muted">
                        群组: ${instance.groups_count} | 好友: ${instance.friends_count}
                    </small>
                </div>
                <span class="badge ${statusClass}">${statusText}</span>
            </div>
        `;
    }).join('');
    
    container.innerHTML = html;
}

function getStatusClass(status) {
    const statusMap = {
        'online': 'bg-success',
        'offline': 'bg-secondary',
        'connecting': 'bg-warning',
        'error': 'bg-danger',
        'maintenance': 'bg-info'
    };
    return statusMap[status] || 'bg-secondary';
}

function getStatusText(status) {
    const statusMap = {
        'online': '在线',
        'offline': '离线',
        'connecting': '连接中',
        'error': '错误',
        'maintenance': '维护中'
    };
    return statusMap[status] || '未知';
}

function updateProgressBar(progressId, textId, value) {
    const progressBar = document.getElementById(progressId);
    const textElement = document.getElementById(textId);
    
    progressBar.style.width = value + '%';
    textElement.textContent = value.toFixed(1) + '%';
    
    // 根据使用率设置颜色
    progressBar.className = 'progress-bar';
    if (value > 80) {
        progressBar.classList.add('bg-danger');
    } else if (value > 60) {
        progressBar.classList.add('bg-warning');
    } else {
        progressBar.classList.add('bg-success');
    }
}

function refreshBotInstances() {
    loadBotInstances();
    showToast('已刷新机器人实例状态', 'success');
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
{% endblock %}
