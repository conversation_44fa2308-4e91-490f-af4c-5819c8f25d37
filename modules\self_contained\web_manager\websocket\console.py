"""
控制台WebSocket处理

提供实时日志显示、命令执行等功能
"""

import json
from datetime import datetime

from creart import create
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger

from core.bot import Umaru

from ..services.auth_service import auth_service


class ConsoleWebSocket:
    """控制台WebSocket管理器"""

    def __init__(self):
        # 存储活动的WebSocket连接
        self.active_connections: set[WebSocket] = set()
        # 日志缓存
        self.log_buffer: list[dict] = []
        self.max_buffer_size = 1000

        # 获取Umaru实例以访问日志
        self.umaru = create(Umaru)

        # 设置日志拦截器
        self._setup_log_interceptor()

    def _setup_log_interceptor(self):
        """设置日志拦截器以实时捕获日志"""
        def log_sink(message):
            """日志接收器"""
            try:
                # 解析日志记录
                record = message.record
                log_entry = {
                    "type": "log",
                    "level": record["level"].name,
                    "message": record["message"],
                    "timestamp": record["time"].isoformat(),
                    "source": record.get("name", "unknown"),
                }

                # 添加到缓存
                self.log_buffer.append(log_entry)
                if len(self.log_buffer) > self.max_buffer_size:
                    self.log_buffer.pop(0)

                # 实时广播到WebSocket连接
                if self.active_connections:
                    import asyncio
                    try:
                        # 在事件循环中运行异步广播
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            asyncio.create_task(self._broadcast_log_entry(log_entry))
                    except Exception:
                        # 如果没有事件循环，忽略
                        pass

            except Exception as e:
                # 避免日志拦截器本身产生错误
                pass

        # 添加日志处理器
        logger.add(log_sink, level="DEBUG", format="{message}")

    async def _broadcast_log_entry(self, log_entry: dict):
        """广播单个日志条目到所有WebSocket连接"""
        if not self.active_connections:
            return

        disconnected = set()
        for websocket in self.active_connections:
            try:
                await websocket.send_text(json.dumps(log_entry))
            except Exception:
                disconnected.add(websocket)

        # 清理断开的连接
        self.active_connections -= disconnected

    async def connect(self, websocket: WebSocket, token: str) -> bool:
        """建立WebSocket连接"""
        try:
            # 验证用户身份
            user = await auth_service.verify_session(token)
            if not user:
                await websocket.close(code=4001, reason="Unauthorized")
                return False

            await websocket.accept()
            self.active_connections.add(websocket)

            # 发送历史日志
            await self.send_log_history(websocket)

            logger.info(f"用户 {user.username} 连接到控制台WebSocket")
            return True

        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            return False

    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        self.active_connections.discard(websocket)
        logger.info("控制台WebSocket连接已断开")

    async def send_log_history(self, websocket: WebSocket):
        """发送历史日志"""
        try:
            # 发送缓存的日志
            for log_entry in self.log_buffer[-50:]:  # 只发送最近50条
                await websocket.send_text(json.dumps(log_entry))

            # 发送Umaru实例中的日志
            if hasattr(self.umaru, "logs"):
                for log_line in self.umaru.logs[-50:]:
                    log_entry = {
                        "type": "log",
                        "level": "INFO",
                        "message": log_line,
                        "timestamp": datetime.now().isoformat(),
                        "source": "bot",
                    }
                    await websocket.send_text(json.dumps(log_entry))

        except Exception as e:
            logger.error(f"发送日志历史失败: {e}")

    async def broadcast_log(self, level: str, message: str, source: str = "system"):
        """广播日志消息到所有连接"""
        if not self.active_connections:
            return

        log_entry = {
            "type": "log",
            "level": level,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "source": source,
        }

        # 添加到缓存
        self.log_buffer.append(log_entry)
        if len(self.log_buffer) > self.max_buffer_size:
            self.log_buffer.pop(0)

        # 广播到所有连接
        disconnected = set()
        for websocket in self.active_connections:
            try:
                await websocket.send_text(json.dumps(log_entry))
            except:
                disconnected.add(websocket)

        # 清理断开的连接
        self.active_connections -= disconnected

    async def handle_command(self, websocket: WebSocket, command_data: dict):
        """处理命令执行"""
        try:
            command = command_data.get("command", "").strip()
            if not command:
                return

            # 记录命令执行
            await self.broadcast_log("INFO", f"执行命令: {command}", "command")

            # 这里可以添加命令执行逻辑
            # 例如：重启机器人、查看状态等

            if command == "status":
                # 获取系统状态
                status_info = await self.get_system_status()
                response = {
                    "type": "command_result",
                    "command": command,
                    "result": status_info,
                    "timestamp": datetime.now().isoformat(),
                }
                await websocket.send_text(json.dumps(response))

            elif command.startswith("restart"):
                # 重启相关命令
                await self.broadcast_log("WARNING", "重启命令暂未实现", "command")

            else:
                await self.broadcast_log("WARNING", f"未知命令: {command}", "command")

        except Exception as e:
            logger.error(f"处理命令失败: {e}")
            await self.broadcast_log("ERROR", f"命令执行失败: {str(e)}", "command")

    async def get_system_status(self) -> dict:
        """获取系统状态信息"""
        try:
            import psutil

            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")

            # 获取机器人状态
            bot_count = len(self.umaru.apps)
            uptime = datetime.now() - self.umaru.launch_time

            return {
                "cpu_usage": cpu_percent,
                "memory_usage": memory.percent,
                "memory_total": memory.total,
                "memory_used": memory.used,
                "disk_usage": disk.percent,
                "disk_total": disk.total,
                "disk_used": disk.used,
                "bot_count": bot_count,
                "uptime": str(uptime).split(".")[0],
                "message_sent": getattr(self.umaru, "sent_count", 0),
                "message_received": getattr(self.umaru, "received_count", 0),
            }

        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {"error": str(e)}


# 全局实例
console_ws = ConsoleWebSocket()


async def websocket_endpoint(websocket: WebSocket, token: str):
    """WebSocket端点"""
    if not await console_ws.connect(websocket, token):
        return

    try:
        while True:
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                message_type = message.get("type")

                if message_type == "command":
                    await console_ws.handle_command(websocket, message)
                elif message_type == "ping":
                    # 心跳响应
                    await websocket.send_text(json.dumps({"type": "pong"}))

            except json.JSONDecodeError:
                await console_ws.broadcast_log(
                    "ERROR", "收到无效的JSON数据", "websocket"
                )

    except WebSocketDisconnect:
        console_ws.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket处理异常: {e}")
        console_ws.disconnect(websocket)
