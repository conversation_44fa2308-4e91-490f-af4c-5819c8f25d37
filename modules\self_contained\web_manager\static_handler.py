"""
静态文件和模板处理

处理Web管理界面的静态资源和HTML模板
"""

from pathlib import Path

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from .models.auth import UserInfo
from .services.auth_service import auth_service

# 获取模块路径
MODULE_PATH = Path(__file__).parent
STATIC_PATH = MODULE_PATH / "static"
TEMPLATES_PATH = MODULE_PATH / "templates"

# 确保目录存在
STATIC_PATH.mkdir(exist_ok=True)
TEMPLATES_PATH.mkdir(exist_ok=True)

# 创建模板引擎
templates = Jinja2Templates(directory=str(TEMPLATES_PATH))

# 创建路由器
router = APIRouter(tags=["Web界面"])


async def authenticate_from_cookie(request: Request) -> UserInfo | None:
    """从Cookie中获取用户认证信息"""
    try:
        # 尝试从多个来源获取token
        token = None

        # 1. 从Authorization头获取
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

        # 2. 从Cookie获取
        if not token:
            token = request.cookies.get("access_token")

        # 如果没有token，返回None
        if not token:
            return None

        # 验证会话
        current_user = await auth_service.verify_session(token)
        return current_user

    except Exception:
        return None


def setup_static_routes(app):
    """设置静态文件路由"""
    # 静态文件服务
    app.mount("/static", StaticFiles(directory=str(STATIC_PATH)), name="static")


@router.get("/", response_class=HTMLResponse, summary="主页")
async def index(request: Request):
    """主页 - 重定向到登录页面或控制台"""
    return templates.TemplateResponse("index.html", {"request": request})


@router.get("/login", response_class=HTMLResponse, summary="登录页面")
async def login_page(request: Request):
    """登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})


@router.get("/dashboard", response_class=HTMLResponse, summary="控制台主页")
async def dashboard(request: Request):
    """控制台主页"""
    try:
        # 尝试从多个来源获取token
        token = None

        # 1. 从Authorization头获取
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]

        # 2. 从Cookie获取
        if not token:
            token = request.cookies.get("access_token")

        # 如果没有token，重定向到登录页面
        if not token:
            from fastapi.responses import RedirectResponse

            return RedirectResponse(url="/login?from=dashboard", status_code=302)

        # 验证token并获取用户信息
        from .api import auth_service

        current_user = await auth_service.verify_session(token)
        if not current_user:
            # token无效，重定向到登录页面
            from fastapi.responses import RedirectResponse

            return RedirectResponse(url="/login?from=dashboard", status_code=302)

        return templates.TemplateResponse(
            "dashboard.html", {"request": request, "user": current_user}
        )
    except Exception:
        # 任何错误都重定向到登录页面
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url="/login?from=dashboard", status_code=302)


@router.get("/bots", response_class=HTMLResponse, summary="机器人管理页面")
async def bots_page(request: Request):
    """机器人管理页面"""
    # 验证用户认证
    current_user = await authenticate_from_cookie(request)
    if not current_user:
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url="/login?from=bots", status_code=302)

    return templates.TemplateResponse(
        "bots.html", {"request": request, "user": current_user}
    )


@router.get("/console", response_class=HTMLResponse, summary="实时控制台页面")
async def console_page(request: Request):
    """实时控制台页面"""
    # 验证用户认证
    current_user = await authenticate_from_cookie(request)
    if not current_user:
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url="/login?from=console", status_code=302)

    return templates.TemplateResponse(
        "console.html", {"request": request, "user": current_user}
    )


@router.get("/chat", response_class=HTMLResponse, summary="聊天管理页面")
async def chat_page(request: Request):
    """聊天管理页面"""
    # 验证用户认证
    current_user = await authenticate_from_cookie(request)
    if not current_user:
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url="/login?from=chat", status_code=302)

    return templates.TemplateResponse(
        "chat.html", {"request": request, "user": current_user}
    )


@router.get("/config", response_class=HTMLResponse, summary="配置管理页面")
async def config_page(request: Request):
    """配置管理页面"""
    # 验证用户认证
    current_user = await authenticate_from_cookie(request)
    if not current_user:
        from fastapi.responses import RedirectResponse

        return RedirectResponse(url="/login?from=config", status_code=302)

    return templates.TemplateResponse(
        "config.html", {"request": request, "user": current_user}
    )
