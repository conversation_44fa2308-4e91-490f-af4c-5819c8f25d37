"""
机器人管理服务

处理机器人实例管理、状态监控、群组管理等业务逻辑
"""

from creart import create
from graia.ariadne import Ariadne
from loguru import logger

from core.bot import Umaru
from core.config import GlobalConfig

from ..models.bot import (
    BotInstanceInfo,
    BotStatistics,
    BotStatus,
)


class BotService:
    """机器人管理服务类"""

    def __init__(self):
        self.umaru = create(Umaru)
        self.config = create(GlobalConfig)

    async def get_bot_instances(self) -> list[BotInstanceInfo]:
        """获取所有机器人实例信息"""
        try:
            instances = []

            for app in self.umaru.apps:
                try:
                    # 获取基本信息
                    account = app.account

                    # 尝试获取机器人信息
                    nickname = None
                    avatar = None
                    try:
                        bot_profile = await app.get_bot_profile()
                        nickname = bot_profile.nickname

                        # 获取机器人头像
                        try:
                            from utils.image import get_user_avatar_url

                            avatar = await get_user_avatar_url(account)
                        except Exception as e:
                            logger.debug(f"获取机器人 {account} 头像失败: {e}")
                    except Exception as e:
                        logger.debug(f"获取机器人 {account} 信息失败: {e}")

                    # 判断连接状态
                    status = (
                        BotStatus.ONLINE
                        if app.connection.status.available
                        else BotStatus.OFFLINE
                    )

                    # 获取群组和好友数量
                    try:
                        groups = await app.get_group_list()
                        friends = await app.get_friend_list()
                        groups_count = len(groups)
                        friends_count = len(friends)
                    except Exception as e:
                        logger.debug(f"获取机器人 {account} 群组好友列表失败: {e}")
                        groups_count = 0
                        friends_count = 0

                    # 获取消息统计
                    message_sent = getattr(self.umaru, "sent_count", 0)
                    message_received = getattr(self.umaru, "received_count", 0)

                    instance = BotInstanceInfo(
                        account=account,
                        nickname=nickname,
                        avatar=avatar,
                        status=status,
                        connection_time=self.umaru.launch_time,
                        message_sent=message_sent,
                        message_received=message_received,
                        groups_count=groups_count,
                        friends_count=friends_count,
                    )

                    instances.append(instance)

                except Exception as e:
                    # 如果获取信息失败，创建错误状态的实例
                    instance = BotInstanceInfo(
                        account=getattr(app, "account", 0),
                        nickname=None,
                        status=BotStatus.ERROR,
                        error_message=str(e),
                        groups_count=0,
                        friends_count=0,
                    )
                    instances.append(instance)

            return instances
        except Exception as e:
            from loguru import logger

            logger.error(f"获取机器人实例失败: {e}")
            return []

    async def get_bot_groups(self, bot_account: int) -> list:
        """
        获取指定机器人的群组列表

        Args:
            bot_account: 机器人账号

        Returns:
            群组列表
        """
        try:
            # 查找指定的机器人实例
            target_app = None
            for app in self.umaru.apps:
                if app.account == bot_account:
                    target_app = app
                    break

            if not target_app:
                return []

            # 使用正确的API获取群组列表
            import asyncio

            group_list = await target_app.get_group_list()

            # 并发获取群组详细信息
            async def get_group_info(group):
                # 尝试获取群成员数量
                try:
                    member_list = await target_app.get_member_list(group.id)
                    member_count = len(member_list)
                except Exception:
                    member_count = 0

                # 获取群头像
                avatar_base64 = ""
                try:
                    from utils.image import get_img_base64_str

                    avatar_bytes = await group.get_avatar()
                    if avatar_bytes:
                        avatar_base64 = get_img_base64_str(avatar_bytes)
                except Exception as e:
                    from loguru import logger

                    logger.debug(f"获取群 {group.id} 头像失败: {e}")

                return {
                    "id": str(group.id),
                    "name": group.name,
                    "member_count": member_count,
                    "avatar": avatar_base64,
                    "type": "group",
                }

            # 使用 asyncio.gather 并发获取所有群组信息
            groups = await asyncio.gather(
                *[get_group_info(group) for group in group_list], return_exceptions=True
            )

            # 过滤掉异常结果
            return [group for group in groups if not isinstance(group, Exception)]

        except Exception as e:
            from loguru import logger

            logger.error(f"获取机器人群组列表失败: {e}")
            return []

    async def get_bot_friends(self, bot_account: int) -> list:
        """
        获取指定机器人的好友列表

        Args:
            bot_account: 机器人账号

        Returns:
            好友列表
        """
        try:
            # 查找指定的机器人实例
            target_app = None
            for app in self.umaru.apps:
                if app.account == bot_account:
                    target_app = app
                    break

            if not target_app:
                return []

            # 使用正确的API获取好友列表
            friend_list = await target_app.get_friend_list()
            friends = []
            for friend in friend_list:
                # 获取好友头像URL
                avatar_url = ""
                try:
                    from utils.image import get_user_avatar_url

                    avatar_url = await get_user_avatar_url(friend.id)
                except Exception as e:
                    from loguru import logger

                    logger.debug(f"获取好友 {friend.id} 头像失败: {e}")

                # 处理好友昵称，如果为空则使用ID
                friend_name = (
                    friend.nickname.strip() if friend.nickname else str(friend.id)
                )

                friends.append(
                    {
                        "id": str(friend.id),
                        "name": friend_name,
                        "avatar": avatar_url,
                        "type": "friend",
                    }
                )

            return friends

        except Exception as e:
            from loguru import logger

            logger.error(f"获取机器人好友列表失败: {e}")
            return []

    async def get_bot_statistics(self) -> BotStatistics:
        """获取机器人统计信息"""
        try:
            instances = await self.get_bot_instances()

            total_accounts = len(instances)
            online_accounts = len(
                [i for i in instances if i.status == BotStatus.ONLINE]
            )

            total_groups = sum(i.groups_count for i in instances)
            total_friends = sum(i.friends_count for i in instances)

            messages_total = sum(i.message_sent + i.message_received for i in instances)

            # 计算运行时间
            import datetime as dt

            uptime_delta = dt.datetime.now() - self.umaru.launch_time
            uptime = str(uptime_delta).split(".")[0]  # 去掉微秒

            # 获取系统资源使用情况
            import psutil

            memory_usage = psutil.virtual_memory().percent
            cpu_usage = psutil.cpu_percent()

            return BotStatistics(
                total_accounts=total_accounts,
                online_accounts=online_accounts,
                total_groups=total_groups,
                total_friends=total_friends,
                messages_today=0,  # 这里需要从数据库统计
                messages_total=messages_total,
                uptime=uptime,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
            )

        except Exception as e:
            logger.error(f"获取机器人统计信息失败: {e}")
            return BotStatistics(
                total_accounts=0,
                online_accounts=0,
                total_groups=0,
                total_friends=0,
                messages_today=0,
                messages_total=0,
                uptime="00:00:00",
                memory_usage=0.0,
                cpu_usage=0.0,
            )

    def _get_app_by_account(self, account: int) -> Ariadne | None:
        """根据账号获取Ariadne实例"""
        for app in self.umaru.apps:
            if app.account == account:
                return app
        return None

    async def restart_bot_instance(self, account: int) -> bool:
        """重启指定机器人实例"""
        try:
            # 这里需要实现重启逻辑
            # 由于Ariadne的限制，可能需要重新设计这部分
            logger.info(f"尝试重启机器人实例: {account}")
            return True
        except Exception as e:
            logger.error(f"重启机器人实例失败: {e}")
            return False

    async def send_message_to_group(
        self, account: int, group_id: int, message: str
    ) -> bool:
        """发送消息到群组"""
        try:
            app = self._get_app_by_account(account)
            if not app:
                return False

            from graia.ariadne.message.chain import MessageChain

            await app.send_group_message(group_id, MessageChain(message))
            return True

        except Exception as e:
            logger.error(f"发送群组消息失败: {e}")
            return False

    async def send_message_to_friend(
        self, account: int, friend_id: int, message: str
    ) -> bool:
        """发送消息到好友"""
        try:
            app = self._get_app_by_account(account)
            if not app:
                return False

            from graia.ariadne.message.chain import MessageChain

            await app.send_friend_message(friend_id, MessageChain(message))
            return True

        except Exception as e:
            logger.error(f"发送好友消息失败: {e}")
            return False

    async def send_message(
        self, bot_account: int, chat_type: str, chat_id: int, message: str
    ) -> bool:
        """统一的发送消息接口"""
        try:
            if chat_type == "groups":
                return await self.send_message_to_group(bot_account, chat_id, message)
            elif chat_type == "friends":
                return await self.send_message_to_friend(bot_account, chat_id, message)
            else:
                logger.error(f"不支持的聊天类型: {chat_type}")
                return False
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
