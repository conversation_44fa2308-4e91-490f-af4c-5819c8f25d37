{% extends "base.html" %}

{% block title %}机器人管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-robot"></i> 机器人管理</h2>
                    <p class="text-muted">管理和监控机器人实例状态、查看连接信息、管理群组和好友</p>
                    <div class="alert alert-info mt-2" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <strong>功能说明：</strong>
                        在这里您可以查看所有机器人账号的运行状态、连接情况、群组和好友数量，以及进行基本的管理操作。
                    </div>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="refreshBots()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 机器人实例列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> 机器人实例
                    </h5>
                </div>
                <div class="card-body">
                    <div id="bots-loading" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载机器人实例...</p>
                    </div>
                    <div id="bots-content" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>QQ号</th>
                                        <th>昵称</th>
                                        <th>状态</th>
                                        <th>群组数</th>
                                        <th>好友数</th>
                                        <th>最后活动</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="bots-table-body">
                                    <!-- 动态加载内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="bots-error" style="display: none;" class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="bots-error-message">加载失败</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 机器人详情模态框 -->
    <div class="modal fade" id="botDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">机器人详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="bot-detail-content">
                        <!-- 动态加载内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时获取机器人列表
document.addEventListener('DOMContentLoaded', function() {
    loadBots();
});

async function loadBots() {
    try {
        showLoading();
        
        const response = await fetch('/api/v1/bot/instances', {
            headers: {
                'Authorization': `Bearer ${getToken()}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        displayBots(data);
        
    } catch (error) {
        console.error('加载机器人列表失败:', error);
        showError(error.message);
    }
}

function displayBots(bots) {
    const tbody = document.getElementById('bots-table-body');
    tbody.innerHTML = '';
    
    if (!bots || bots.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无机器人实例</td></tr>';
    } else {
        bots.forEach(bot => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${bot.qq_id}</strong></td>
                <td>${bot.nickname || '-'}</td>
                <td>
                    <span class="badge ${bot.status === 'online' ? 'bg-success' : 'bg-secondary'}">
                        ${bot.status === 'online' ? '在线' : '离线'}
                    </span>
                </td>
                <td>${bot.group_count || 0}</td>
                <td>${bot.friend_count || 0}</td>
                <td>${bot.last_activity || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="showBotDetail('${bot.qq_id}')">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    hideLoading();
}

function showBotDetail(qqId) {
    // TODO: 实现机器人详情显示
    const modal = new bootstrap.Modal(document.getElementById('botDetailModal'));
    document.getElementById('bot-detail-content').innerHTML = `
        <p>机器人 ${qqId} 的详细信息</p>
        <p class="text-muted">功能开发中...</p>
    `;
    modal.show();
}

function refreshBots() {
    loadBots();
}

function showLoading() {
    document.getElementById('bots-loading').style.display = 'block';
    document.getElementById('bots-content').style.display = 'none';
    document.getElementById('bots-error').style.display = 'none';
}

function hideLoading() {
    document.getElementById('bots-loading').style.display = 'none';
    document.getElementById('bots-content').style.display = 'block';
}

function showError(message) {
    document.getElementById('bots-loading').style.display = 'none';
    document.getElementById('bots-content').style.display = 'none';
    document.getElementById('bots-error').style.display = 'block';
    document.getElementById('bots-error-message').textContent = message;
}

function getToken() {
    // 优先从localStorage获取，如果没有则从Cookie获取
    return localStorage.getItem('access_token') || getCookie('access_token');
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}
</script>
{% endblock %}
