{% extends "base.html" %}

{% block title %}配置管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-cogs"></i> 配置管理</h2>
                    <p class="text-muted">管理机器人配置和系统设置</p>
                </div>
                <div>
                    <button class="btn btn-success" onclick="saveConfig()">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置分类 -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">配置分类</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action active" onclick="showConfigSection('basic')">
                        <i class="fas fa-cog"></i> 基础配置
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="showConfigSection('bot')">
                        <i class="fas fa-robot"></i> 机器人配置
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="showConfigSection('api')">
                        <i class="fas fa-plug"></i> API配置
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="showConfigSection('security')">
                        <i class="fas fa-shield-alt"></i> 安全配置
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="showConfigSection('advanced')">
                        <i class="fas fa-tools"></i> 高级配置
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0" id="config-section-title">基础配置</h5>
                </div>
                <div class="card-body">
                    <div id="config-loading" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载配置...</p>
                    </div>
                    
                    <div id="config-content" style="display: none;">
                        <!-- 基础配置 -->
                        <div id="basic-config" class="config-section">
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">机器人名称</label>
                                            <input type="text" class="form-control" id="bot-name" placeholder="小埋">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">管理员QQ</label>
                                            <input type="text" class="form-control" id="admin-qq" placeholder="123456789">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">日志级别</label>
                                            <select class="form-select" id="log-level">
                                                <option value="DEBUG">DEBUG</option>
                                                <option value="INFO" selected>INFO</option>
                                                <option value="WARNING">WARNING</option>
                                                <option value="ERROR">ERROR</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">时区</label>
                                            <select class="form-select" id="timezone">
                                                <option value="Asia/Shanghai" selected>Asia/Shanghai</option>
                                                <option value="UTC">UTC</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 机器人配置 -->
                        <div id="bot-config" class="config-section" style="display: none;">
                            <form>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto-accept-friend">
                                        <label class="form-check-label" for="auto-accept-friend">
                                            自动接受好友请求
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto-accept-group">
                                        <label class="form-check-label" for="auto-accept-group">
                                            自动接受群邀请
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">命令前缀</label>
                                    <input type="text" class="form-control" id="command-prefix" placeholder="-">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">消息处理间隔 (毫秒)</label>
                                    <input type="number" class="form-control" id="message-interval" placeholder="1000">
                                </div>
                            </form>
                        </div>

                        <!-- API配置 -->
                        <div id="api-config" class="config-section" style="display: none;">
                            <form>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">API端口</label>
                                            <input type="number" class="form-control" id="api-port" placeholder="3301">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">API主机</label>
                                            <input type="text" class="form-control" id="api-host" placeholder="127.0.0.1">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="api-expose">
                                        <label class="form-check-label" for="api-expose">
                                            允许外部访问API
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 安全配置 -->
                        <div id="security-config" class="config-section" style="display: none;">
                            <form>
                                <div class="mb-3">
                                    <label class="form-label">会话超时时间 (小时)</label>
                                    <input type="number" class="form-control" id="session-timeout" placeholder="24">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">最大登录尝试次数</label>
                                    <input type="number" class="form-control" id="max-login-attempts" placeholder="5">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-rate-limit">
                                        <label class="form-check-label" for="enable-rate-limit">
                                            启用API速率限制
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 高级配置 -->
                        <div id="advanced-config" class="config-section" style="display: none;">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>警告:</strong> 高级配置可能影响系统稳定性，请谨慎修改。
                            </div>
                            <form>
                                <div class="mb-3">
                                    <label class="form-label">数据库连接池大小</label>
                                    <input type="number" class="form-control" id="db-pool-size" placeholder="10">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">缓存过期时间 (秒)</label>
                                    <input type="number" class="form-control" id="cache-expire" placeholder="3600">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">自定义配置 (JSON)</label>
                                    <textarea class="form-control" id="custom-config" rows="10" placeholder='{"key": "value"}'></textarea>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div id="config-error" style="display: none;" class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="config-error-message">加载配置失败</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentSection = 'basic';
let configData = {};

// 页面加载时获取配置
document.addEventListener('DOMContentLoaded', function() {
    loadConfig();
});

async function loadConfig() {
    try {
        showLoading();
        
        const response = await fetch('/api/v1/config', {
            headers: {
                'Authorization': `Bearer ${getToken()}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        configData = await response.json();
        populateConfigForm();
        hideLoading();
        
    } catch (error) {
        console.error('加载配置失败:', error);
        showError(error.message);
    }
}

function populateConfigForm() {
    // 填充基础配置
    if (configData.basic) {
        document.getElementById('bot-name').value = configData.basic.bot_name || '';
        document.getElementById('admin-qq').value = configData.basic.admin_qq || '';
        document.getElementById('log-level').value = configData.basic.log_level || 'INFO';
        document.getElementById('timezone').value = configData.basic.timezone || 'Asia/Shanghai';
    }
    
    // 填充机器人配置
    if (configData.bot) {
        document.getElementById('auto-accept-friend').checked = configData.bot.auto_accept_friend || false;
        document.getElementById('auto-accept-group').checked = configData.bot.auto_accept_group || false;
        document.getElementById('command-prefix').value = configData.bot.command_prefix || '';
        document.getElementById('message-interval').value = configData.bot.message_interval || '';
    }
    
    // 填充API配置
    if (configData.api) {
        document.getElementById('api-port').value = configData.api.port || '';
        document.getElementById('api-host').value = configData.api.host || '';
        document.getElementById('api-expose').checked = configData.api.expose || false;
    }
    
    // 填充安全配置
    if (configData.security) {
        document.getElementById('session-timeout').value = configData.security.session_timeout || '';
        document.getElementById('max-login-attempts').value = configData.security.max_login_attempts || '';
        document.getElementById('enable-rate-limit').checked = configData.security.enable_rate_limit || false;
    }
    
    // 填充高级配置
    if (configData.advanced) {
        document.getElementById('db-pool-size').value = configData.advanced.db_pool_size || '';
        document.getElementById('cache-expire').value = configData.advanced.cache_expire || '';
        document.getElementById('custom-config').value = JSON.stringify(configData.advanced.custom || {}, null, 2);
    }
}

function showConfigSection(section) {
    // 更新导航状态
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 隐藏所有配置区域
    document.querySelectorAll('.config-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示选中的配置区域
    document.getElementById(`${section}-config`).style.display = 'block';
    
    // 更新标题
    const titles = {
        'basic': '基础配置',
        'bot': '机器人配置',
        'api': 'API配置',
        'security': '安全配置',
        'advanced': '高级配置'
    };
    document.getElementById('config-section-title').textContent = titles[section];
    
    currentSection = section;
}

async function saveConfig() {
    try {
        // 收集表单数据
        const formData = collectFormData();
        
        const response = await fetch('/api/v1/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getToken()}`
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // 显示成功消息
        showToast('配置保存成功', 'success');
        
    } catch (error) {
        console.error('保存配置失败:', error);
        showToast('保存配置失败: ' + error.message, 'error');
    }
}

function collectFormData() {
    return {
        basic: {
            bot_name: document.getElementById('bot-name').value,
            admin_qq: document.getElementById('admin-qq').value,
            log_level: document.getElementById('log-level').value,
            timezone: document.getElementById('timezone').value
        },
        bot: {
            auto_accept_friend: document.getElementById('auto-accept-friend').checked,
            auto_accept_group: document.getElementById('auto-accept-group').checked,
            command_prefix: document.getElementById('command-prefix').value,
            message_interval: parseInt(document.getElementById('message-interval').value) || 1000
        },
        api: {
            port: parseInt(document.getElementById('api-port').value) || 3301,
            host: document.getElementById('api-host').value,
            expose: document.getElementById('api-expose').checked
        },
        security: {
            session_timeout: parseInt(document.getElementById('session-timeout').value) || 24,
            max_login_attempts: parseInt(document.getElementById('max-login-attempts').value) || 5,
            enable_rate_limit: document.getElementById('enable-rate-limit').checked
        },
        advanced: {
            db_pool_size: parseInt(document.getElementById('db-pool-size').value) || 10,
            cache_expire: parseInt(document.getElementById('cache-expire').value) || 3600,
            custom: JSON.parse(document.getElementById('custom-config').value || '{}')
        }
    };
}

function showLoading() {
    document.getElementById('config-loading').style.display = 'block';
    document.getElementById('config-content').style.display = 'none';
    document.getElementById('config-error').style.display = 'none';
}

function hideLoading() {
    document.getElementById('config-loading').style.display = 'none';
    document.getElementById('config-content').style.display = 'block';
}

function showError(message) {
    document.getElementById('config-loading').style.display = 'none';
    document.getElementById('config-content').style.display = 'none';
    document.getElementById('config-error').style.display = 'block';
    document.getElementById('config-error-message').textContent = message;
}

function showToast(message, type) {
    // 简单的提示实现
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const toast = document.createElement('div');
    toast.className = `alert ${alertClass} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'times'}-circle"></i>
        ${message}
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function getToken() {
    // 优先从localStorage获取，如果没有则从Cookie获取
    return localStorage.getItem('access_token') || getCookie('access_token');
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}
</script>
{% endblock %}
