"""
配置管理相关数据模型
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel
from enum import Enum

class ConfigType(str, Enum):
    """配置类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"
    PASSWORD = "password"

class ConfigItem(BaseModel):
    """配置项"""
    key: str
    value: Any
    type: ConfigType
    description: Optional[str] = None
    default_value: Any = None
    required: bool = False
    options: Optional[List[Any]] = None  # 可选值列表
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    pattern: Optional[str] = None  # 正则表达式验证
    category: str = "general"

class ModuleConfig(BaseModel):
    """模块配置"""
    module_name: str
    display_name: str
    description: Optional[str] = None
    enabled: bool = True
    default_switch: bool = True
    default_notice: bool = True
    config_items: List[ConfigItem] = []
    groups: Dict[str, Dict[str, Any]] = {}  # 群组配置

class SystemConfig(BaseModel):
    """系统配置"""
    master: int
    bot_accounts: List[Union[int, str, Dict[str, Any]]]
    default_account: Union[int, str]
    mirai_host: str
    verify_key: str
    test_group: int
    proxy: Optional[str] = None
    api_port: int = 8080
    api_expose: bool = False
    web_manager_api: bool = True
    web_manager_auto_boot: bool = False
    db_link: str
    log_related: Dict[str, int]
    auto_upgrade: bool = False
    functions: Dict[str, Any] = {}
    group_msg_log: bool = True
    debug_mode: bool = False

class ConfigBackup(BaseModel):
    """配置备份"""
    id: int
    name: str
    description: Optional[str] = None
    config_data: Dict[str, Any]
    created_at: str
    created_by: str

class ConfigValidationResult(BaseModel):
    """配置验证结果"""
    valid: bool
    errors: List[str] = []
    warnings: List[str] = []

class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    key: str
    value: Any
    validate_only: bool = False
