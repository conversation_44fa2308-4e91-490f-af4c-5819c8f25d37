"""
认证相关数据模型
"""

from datetime import datetime

from pydantic import BaseModel
from sqlalchemy import Boolean, Column, DateTime, Integer, String, Text

from core.orm import orm


class WebUser(orm.Base):
    """Web管理用户表"""

    __tablename__ = "web_users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    qq_id = Column(Integer, nullable=True, comment="关联的QQ号")
    role = Column(String(20), default="user", comment="用户角色: master/admin/user")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    login_count = Column(Integer, default=0, comment="登录次数")


class WebSession(orm.Base):
    """Web会话表"""

    __tablename__ = "web_sessions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), unique=True, nullable=False, comment="会话ID")
    user_id = Column(Integer, nullable=False, comment="用户ID")
    token = Column(Text, nullable=False, comment="JWT Token")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    is_active = Column(Boolean, default=True, comment="是否有效")


# Pydantic模型用于API
class LoginRequest(BaseModel):
    """登录请求模型"""

    username: str
    password: str
    remember_me: bool = False


class TokenResponse(BaseModel):
    """Token响应模型"""

    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: dict


class UserInfo(BaseModel):
    """用户信息模型"""

    id: int
    username: str
    qq_id: int | None = None
    role: str
    is_active: bool
    created_at: datetime
    last_login: datetime | None = None
    login_count: int


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""

    old_password: str
    new_password: str


class CreateUserRequest(BaseModel):
    """创建用户请求"""

    username: str
    password: str
    qq_id: int | None = None
    role: str = "user"
