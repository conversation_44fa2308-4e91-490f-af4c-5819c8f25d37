"""
聊天管理API

提供聊天统计、消息管理等功能
"""

from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from ..api import get_current_user
from ..models.auth import UserInfo

# 创建路由器
router = APIRouter(prefix="/api/v1/chat", tags=["聊天管理"])


class ChatStatistics(BaseModel):
    """聊天统计信息"""

    today_messages: int
    active_groups: int
    online_friends: int
    message_rate: float


class ChatInfo(BaseModel):
    """聊天信息"""

    id: str
    name: str
    type: str  # 'group' or 'friend'
    last_message: str | None = None
    last_time: str | None = None
    unread_count: int = 0


class MessageInfo(BaseModel):
    """消息信息"""

    id: str
    sender_name: str
    content: str
    timestamp: str
    message_type: str = "text"
    sender_avatar: str = ""


class ChatDetail(BaseModel):
    """聊天详情"""

    chat_info: ChatInfo
    messages: list[MessageInfo]


@router.get("/statistics", response_model=ChatStatistics, summary="获取聊天统计")
async def get_chat_statistics(current_user: UserInfo = Depends(get_current_user)):
    """获取聊天统计信息"""
    try:
        # 这里应该从数据库或缓存中获取真实数据
        # 目前返回模拟数据

        # 模拟统计数据
        statistics = ChatStatistics(
            today_messages=156, active_groups=8, online_friends=12, message_rate=2.3
        )

        return statistics

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取聊天统计失败: {str(e)}")


@router.get("/list", response_model=list[ChatInfo], summary="获取聊天列表")
async def get_chat_list(current_user: UserInfo = Depends(get_current_user)):
    """获取聊天列表"""
    try:
        # 这里应该从数据库中获取真实的聊天列表
        # 目前返回模拟数据

        chats = [
            ChatInfo(
                id="group_123456",
                name="测试群组1",
                type="group",
                last_message="这是最后一条消息",
                last_time="14:30",
                unread_count=3,
            ),
            ChatInfo(
                id="group_789012",
                name="开发讨论群",
                type="group",
                last_message="代码已提交",
                last_time="13:45",
                unread_count=0,
            ),
            ChatInfo(
                id="friend_111222",
                name="张三",
                type="friend",
                last_message="好的，收到",
                last_time="12:20",
                unread_count=1,
            ),
            ChatInfo(
                id="friend_333444",
                name="李四",
                type="friend",
                last_message="明天见",
                last_time="11:15",
                unread_count=0,
            ),
        ]

        return chats

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取聊天列表失败: {str(e)}")


@router.get(
    "/{bot_account}/{chat_type}/{chat_id}/messages",
    response_model=ChatDetail,
    summary="获取聊天消息",
)
async def get_chat_messages(
    bot_account: int,
    chat_type: str,  # 'groups' or 'friends'
    chat_id: str,
    limit: int = 50,
    offset: int = 0,
    current_user: UserInfo = Depends(get_current_user),
):
    """获取指定聊天的消息记录"""
    try:
        if chat_type not in ["groups", "friends"]:
            raise HTTPException(
                status_code=400, detail="chat_type必须是'groups'或'friends'"
            )

        # 获取聊天信息
        from ..services.bot_service import BotService

        bot_service = BotService()

        # 获取聊天基本信息
        if chat_type == "groups":
            groups = await bot_service.get_bot_groups(bot_account)
            chat_info_data = next((g for g in groups if str(g["id"]) == chat_id), None)
            if not chat_info_data:
                raise HTTPException(status_code=404, detail="群组不存在")
            chat_name = chat_info_data["name"]
        else:
            friends = await bot_service.get_bot_friends(bot_account)
            chat_info_data = next((f for f in friends if str(f["id"]) == chat_id), None)
            if not chat_info_data:
                raise HTTPException(status_code=404, detail="好友不存在")
            chat_name = chat_info_data["name"]

        # 构建聊天信息
        chat_info = ChatInfo(
            id=chat_id,
            name=chat_name,
            type=chat_type[:-1],  # 'groups' -> 'group', 'friends' -> 'friend'
            last_message="暂无消息",
            last_time="",
            unread_count=0,
        )

        # 从数据库获取历史消息
        messages = []
        if chat_type == "groups":
            messages = await _get_group_history_messages(int(chat_id), limit, offset)
        else:
            # 好友消息暂时返回示例消息，因为ChatRecord表只记录群消息
            messages = [
                MessageInfo(
                    id="msg_001",
                    sender_name="系统",
                    content="欢迎使用小埋Bot聊天管理功能！",
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    message_type="text",
                ),
                MessageInfo(
                    id="msg_002",
                    sender_name="小埋",
                    content="您可以在这里查看和发送消息",
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    message_type="text",
                ),
            ]

        return ChatDetail(chat_info=chat_info, messages=messages)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取聊天消息失败: {str(e)}")


class SendMessageRequest(BaseModel):
    """发送消息请求"""

    message: str


@router.post("/{bot_account}/{chat_type}/{chat_id}/send", summary="发送消息")
async def send_message(
    bot_account: int,
    chat_type: str,  # 'groups' or 'friends'
    chat_id: str,
    request: SendMessageRequest,
    current_user: UserInfo = Depends(get_current_user),
):
    """发送消息到指定聊天"""
    try:
        if chat_type not in ["groups", "friends"]:
            raise HTTPException(
                status_code=400, detail="chat_type必须是'groups'或'friends'"
            )

        if not request.message.strip():
            raise HTTPException(status_code=400, detail="消息内容不能为空")

        # 获取机器人实例
        from ..services.bot_service import BotService

        bot_service = BotService()

        # 发送消息
        success = await bot_service.send_message(
            bot_account=bot_account,
            chat_type=chat_type,
            chat_id=int(chat_id),
            message=request.message.strip(),
        )

        if success:
            return {
                "success": True,
                "message": "消息发送成功",
                "message_id": f"msg_{datetime.now().timestamp()}",
                "timestamp": datetime.now().isoformat(),
            }
        else:
            raise HTTPException(status_code=500, detail="消息发送失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")


@router.delete("/{chat_id}/messages/{message_id}", summary="删除消息")
async def delete_message(
    chat_id: str, message_id: str, current_user: UserInfo = Depends(get_current_user)
):
    """删除指定消息"""
    try:
        # 这里应该实现真实的消息删除逻辑
        # 目前只是模拟

        # 检查权限（只有管理员可以删除消息）
        if current_user.role not in ["master", "admin"]:
            raise HTTPException(status_code=403, detail="权限不足")

        # 模拟删除成功
        return {"success": True, "message": "消息删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除消息失败: {str(e)}")


@router.get("/{chat_id}/members", summary="获取聊天成员")
async def get_chat_members(
    chat_id: str, current_user: UserInfo = Depends(get_current_user)
):
    """获取聊天成员列表（仅群组）"""
    try:
        if not chat_id.startswith("group_"):
            raise HTTPException(status_code=400, detail="只有群组才有成员列表")

        # 这里应该从数据库中获取真实的成员列表
        # 目前返回模拟数据

        members = [
            {
                "id": "user_001",
                "name": "用户A",
                "role": "owner",
                "join_time": "2024-01-01 09:00:00",
                "last_active": "2024-01-01 15:30:00",
            },
            {
                "id": "user_002",
                "name": "用户B",
                "role": "admin",
                "join_time": "2024-01-01 09:30:00",
                "last_active": "2024-01-01 15:25:00",
            },
            {
                "id": "user_003",
                "name": "用户C",
                "role": "member",
                "join_time": "2024-01-01 10:00:00",
                "last_active": "2024-01-01 15:20:00",
            },
        ]

        return {"chat_id": chat_id, "member_count": len(members), "members": members}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取成员列表失败: {str(e)}")


@router.get("/bots", summary="获取机器人账号列表")
async def get_bot_accounts(current_user: UserInfo = Depends(get_current_user)):
    """
    获取所有机器人账号列表
    """
    from ..services.bot_service import BotService

    bot_service = BotService()

    try:
        instances = await bot_service.get_bot_instances()
        bots = []

        for instance in instances:
            bots.append(
                {
                    "account": instance.account,
                    "nickname": instance.nickname,
                    "status": instance.status.value,
                    "groups_count": instance.groups_count,
                    "friends_count": instance.friends_count,
                }
            )

        return bots
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取机器人账号列表失败: {str(e)}",
        )


@router.get("/{bot_account}/groups", summary="获取指定机器人的群组列表")
async def get_bot_groups(
    bot_account: int, current_user: UserInfo = Depends(get_current_user)
):
    """
    获取指定机器人的群组列表
    """
    from ..services.bot_service import BotService

    bot_service = BotService()

    try:
        groups = await bot_service.get_bot_groups(bot_account)
        return groups
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取机器人群组列表失败: {str(e)}",
        )


@router.get("/{bot_account}/friends", summary="获取指定机器人的好友列表")
async def get_bot_friends(
    bot_account: int, current_user: UserInfo = Depends(get_current_user)
):
    """
    获取指定机器人的好友列表
    """
    from ..services.bot_service import BotService

    bot_service = BotService()

    try:
        friends = await bot_service.get_bot_friends(bot_account)
        return friends
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取机器人好友列表失败: {str(e)}",
        )


# 用户信息缓存，避免重复获取
_user_info_cache: dict[int, dict[str, str]] = {}


async def _get_user_info_batch(member_ids: set[int]) -> dict[int, dict[str, str]]:
    """
    批量获取用户信息（头像和昵称）

    Args:
        member_ids: 用户ID集合

    Returns:
        用户信息字典 {user_id: {"name": str, "avatar": str}}
    """
    import asyncio

    from utils.image import get_user_avatar_url

    async def get_single_user_info(
        member_id: int, is_bot: bool = False
    ) -> tuple[int, dict[str, str]]:
        """获取单个用户信息"""
        # 检查缓存
        if member_id in _user_info_cache:
            return member_id, _user_info_cache[member_id]

        # 根据是否为机器人设置默认信息
        if is_bot:
            user_info = {"name": f"小埋({member_id})", "avatar": ""}
            # 尝试获取机器人的真实昵称
            try:
                from graia.ariadne import Ariadne

                if member_id in Ariadne.service.connections:
                    app = Ariadne.current(member_id)
                    if app.connection.status.available:
                        bot_profile = await app.get_bot_profile()
                        if bot_profile.nickname:
                            user_info["name"] = bot_profile.nickname
            except Exception:
                pass
        else:
            user_info = {"name": f"用户{member_id}", "avatar": ""}

        try:
            # 获取用户头像
            avatar = await get_user_avatar_url(member_id)
            if avatar:
                user_info["avatar"] = avatar
        except Exception:
            pass

        # 缓存用户信息
        _user_info_cache[member_id] = user_info
        return member_id, user_info

    # 获取机器人账号列表
    bot_accounts = set()
    try:
        from core.config import GlobalConfig

        config = GlobalConfig()
        bot_accounts = {int(account["account"]) for account in config.bot_accounts}
    except Exception:
        pass

    # 过滤出需要获取的用户ID（排除已缓存的）
    uncached_ids = {uid for uid in member_ids if uid not in _user_info_cache}

    # 批量获取未缓存的用户信息
    if uncached_ids:
        results = await asyncio.gather(
            *[
                get_single_user_info(uid, is_bot=(uid in bot_accounts))
                for uid in uncached_ids
            ],
            return_exceptions=True,
        )

        # 处理结果，忽略异常
        for result in results:
            if isinstance(result, tuple):
                uid, info = result
                _user_info_cache[uid] = info

    # 返回所有请求用户的信息
    return {
        uid: _user_info_cache.get(uid, {"name": f"用户{uid}", "avatar": ""})
        for uid in member_ids
    }


async def _get_group_history_messages(
    group_id: int, limit: int = 20, offset: int = 0
) -> list[MessageInfo]:
    """
    从数据库获取群组历史消息（优化版本）

    Args:
        group_id: 群组ID
        limit: 消息数量限制
        offset: 偏移量

    Returns:
        消息列表
    """
    try:
        from graia.ariadne.message.chain import MessageChain
        from sqlalchemy import desc, select

        from core.orm import orm
        from core.orm.tables import ChatRecord

        # 查询历史消息记录
        query = (
            select(ChatRecord)
            .where(ChatRecord.group_id == group_id)
            .order_by(desc(ChatRecord.time))
            .limit(limit)
            .offset(offset)
        )

        records = await orm.fetch_all(query)

        if not records:
            # 如果没有历史消息，返回欢迎消息
            return [
                MessageInfo(
                    id="msg_welcome",
                    sender_name="系统",
                    content="欢迎使用小埋Bot聊天管理功能！这里会显示最近的聊天记录。",
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    message_type="text",
                ),
            ]

        # 第一步：解析所有消息，收集用户ID
        parsed_messages = []
        member_ids = set()

        for record in reversed(records):  # 按时间正序处理
            try:
                # 解析消息链
                message_chain = MessageChain.from_persistent_string(
                    record[4]
                )  # persistent_string
                content = message_chain.display.strip()

                # 如果消息为空，跳过
                if not content:
                    continue

                member_id = record[3]  # member_id
                member_ids.add(member_id)

                parsed_messages.append(
                    {
                        "id": f"msg_{record[0]}",  # id
                        "member_id": member_id,
                        "content": content,
                        "timestamp": record[1].strftime("%Y-%m-%d %H:%M:%S"),  # time
                    }
                )
            except Exception:
                # 如果解析失败，跳过这条消息
                continue

        # 第二步：批量获取所有用户信息
        user_info_map = await _get_user_info_batch(member_ids)

        # 第三步：组装最终的消息列表
        messages: list[MessageInfo] = []
        for msg_data in parsed_messages:
            member_id = msg_data["member_id"]
            user_info = user_info_map.get(
                member_id, {"name": f"用户{member_id}", "avatar": ""}
            )

            messages.append(
                MessageInfo(
                    id=msg_data["id"],
                    sender_name=user_info["name"],
                    content=msg_data["content"],
                    timestamp=msg_data["timestamp"],
                    message_type="text",
                    sender_avatar=user_info["avatar"],
                )
            )

        return messages

    except Exception as e:
        # 如果查询失败，返回默认消息
        return [
            MessageInfo(
                id="msg_error",
                sender_name="系统",
                content=f"暂时无法加载历史消息，请稍后重试。错误：{str(e)}",
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                message_type="text",
            ),
        ]
