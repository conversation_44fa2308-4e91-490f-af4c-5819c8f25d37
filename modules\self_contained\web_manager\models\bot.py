"""
机器人管理相关数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel


class BotStatus(str, Enum):
    """机器人状态枚举"""

    ONLINE = "online"
    OFFLINE = "offline"
    CONNECTING = "connecting"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class BotInstanceInfo(BaseModel):
    """机器人实例信息"""

    account: int
    nickname: str | None = None
    avatar: str | None = None  # 机器人头像URL或base64
    status: BotStatus
    connection_time: datetime | None = None
    last_message_time: datetime | None = None
    message_sent: int = 0
    message_received: int = 0
    groups_count: int = 0
    friends_count: int = 0
    error_message: str | None = None


class GroupInfo(BaseModel):
    """群组信息"""

    id: int
    name: str
    member_count: int
    permission: str  # Member, Administrator, Owner
    active: bool
    last_message_time: datetime | None = None


class FriendInfo(BaseModel):
    """好友信息"""

    id: int
    nickname: str
    remark: str | None = None
    last_message_time: datetime | None = None


class BotCommand(BaseModel):
    """机器人命令"""

    command: str
    target_account: int | None = None  # None表示所有账号
    parameters: dict[str, Any] | None = None


class MessageInfo(BaseModel):
    """消息信息"""

    id: int
    type: str  # group, friend, temp, stranger
    sender_id: int
    sender_name: str
    target_id: int | None = None  # 群号或好友号
    target_name: str | None = None
    content: str
    timestamp: datetime
    bot_account: int


class BotStatistics(BaseModel):
    """机器人统计信息"""

    total_accounts: int
    online_accounts: int
    total_groups: int
    total_friends: int
    messages_today: int
    messages_total: int
    uptime: str
    memory_usage: float
    cpu_usage: float


class AccountConfig(BaseModel):
    """账号配置"""

    account: int
    mirai_host: str | None = None
    verify_key: str | None = None
    auto_start: bool = True
    max_retry: int = 3
    retry_interval: int = 30
