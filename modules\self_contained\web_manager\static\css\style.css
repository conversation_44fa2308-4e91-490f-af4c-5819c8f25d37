/* 小埋Bot Web管理后台 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* 统计卡片样式 */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #146c43) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #d39e00) !important;
}

/* 进度条样式 */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: rgba(0, 0, 0, 0.1);
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.6s ease;
}

/* 按钮样式 */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* 表格样式 */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    border-radius: 0.375rem;
}

/* 登录页面样式 */
.min-vh-100 {
    min-height: 100vh;
}

.card.shadow {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* 输入组样式 */
.input-group-text {
    background-color: var(--light-color);
    border-color: #ced4da;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Toast通知样式 */
.toast {
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 控制台样式 */
.console-container {
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    border-radius: 0.5rem;
    padding: 1rem;
    height: 400px;
    overflow-y: auto;
    font-size: 0.875rem;
    line-height: 1.4;
}

.console-container::-webkit-scrollbar {
    width: 8px;
}

.console-container::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
}

.console-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.console-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.log-entry {
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
    border-left: 3px solid transparent;
    padding-left: 0.5rem;
}

.log-entry.log-info {
    border-left-color: var(--info-color);
}

.log-entry.log-warning {
    border-left-color: var(--warning-color);
    color: #ffc107;
}

.log-entry.log-error {
    border-left-color: var(--danger-color);
    color: #ff6b6b;
}

.log-entry.log-debug {
    border-left-color: var(--secondary-color);
    color: #adb5bd;
}

.log-timestamp {
    color: #6c757d;
    font-size: 0.8rem;
}

/* 聊天界面样式 */
.chat-container {
    height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: white;
}

.message-bubble {
    max-width: 70%;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message-bubble.sent {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 0.25rem;
}

.message-bubble.received {
    background-color: var(--light-color);
    color: var(--dark-color);
    margin-right: auto;
    border-bottom-left-radius: 0.25rem;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

/* 配置管理样式 */
.config-section {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.config-section-header {
    background-color: var(--light-color);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.config-section-header:hover {
    background-color: #e9ecef;
}

.config-section-body {
    padding: 1rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(25, 135, 84, 0.3);
}

.status-indicator.offline {
    background-color: var(--secondary-color);
}

.status-indicator.error {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .console-container {
        height: 300px;
        font-size: 0.8rem;
    }
    
    .chat-container {
        height: 400px;
    }
    
    .message-bubble {
        max-width: 85%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
