"""
Web管理后台API接口

提供RESTful API接口，包括认证、机器人管理、配置管理等
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional

from ..services.auth_service import AuthService
from ..models.auth import UserInfo

# HTTP Bearer认证
security = HTTPBearer()

# 全局认证服务实例
auth_service = AuthService()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserInfo:
    """获取当前用户（依赖注入）"""
    user = await auth_service.verify_session(credentials.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

async def get_current_admin_user(current_user: UserInfo = Depends(get_current_user)) -> UserInfo:
    """获取当前管理员用户（依赖注入）"""
    if current_user.role not in ["admin", "master"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    return current_user

async def get_current_master_user(current_user: UserInfo = Depends(get_current_user)) -> UserInfo:
    """获取当前Master用户（依赖注入）"""
    if current_user.role != "master":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Master permissions required"
        )
    return current_user

# API路由前缀
API_PREFIX = "/api/v1"
