# 小埋Bot Web管理后台

基于xiaomai-bot项目框架的完整Web管理界面，提供机器人实例管理、实时控制台、聊天管理等功能。

## 功能特性

### 🔐 安全认证系统
- JWT Token认证
- 用户角色管理（Master/Admin/User）
- 会话管理和权限控制
- 密码强度验证

### 🤖 机器人实例管理
- 实时查看所有机器人账号状态
- 群组和好友列表管理
- 机器人实例重启功能
- 消息发送功能

### 📊 实时控制台
- WebSocket实时日志显示
- 系统状态监控
- 命令执行功能
- 日志级别过滤

### 💬 聊天管理界面
- 群组聊天界面
- 消息历史查看
- 实时消息收发
- 多账号切换

### ⚙️ 配置管理
- 可视化配置编辑
- 模块开关管理
- 配置验证和备份
- 热重载支持

## 安装和配置

### 1. 启用Web管理功能

在 `config/config.yaml` 中添加或修改以下配置：

```yaml
# Web管理API配置
web_manager_api: true          # 启用Web管理API
web_manager_auto_boot: true    # 自动启动Web管理界面
api_port: 8080                 # API端口
api_expose: false              # 是否对外暴露（生产环境建议false）
```

### 2. 安装依赖

确保已安装以下Python包：

```bash
pip install fastapi jinja2 python-multipart bcrypt python-jose[cryptography]
```

### 3. 启动机器人

正常启动xiaomai-bot，Web管理界面将自动启动。

## 使用说明

### 访问Web界面

启动后访问：`http://localhost:8080/`

### 默认账号

首次使用时，系统会自动创建Master账号：
- 用户名：`master`
- 密码：`admin123`

**⚠️ 重要：首次登录后请立即修改默认密码！**

### 用户角色说明

- **Master**：最高权限，可以管理所有功能和用户
- **Admin**：管理员权限，可以管理机器人和配置
- **User**：普通用户权限，只能查看状态信息

## API接口

### 认证接口

- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/change-password` - 修改密码

### 机器人管理接口

- `GET /api/v1/bot/instances` - 获取机器人实例列表
- `GET /api/v1/bot/instances/{account}/groups` - 获取群组列表
- `GET /api/v1/bot/instances/{account}/friends` - 获取好友列表
- `GET /api/v1/bot/statistics` - 获取统计信息
- `POST /api/v1/bot/instances/{account}/restart` - 重启机器人实例
- `POST /api/v1/bot/send/group` - 发送群组消息
- `POST /api/v1/bot/send/friend` - 发送好友消息

### WebSocket接口

- `ws://localhost:8080/ws/console?token={jwt_token}` - 实时控制台

## 目录结构

```
modules/self_contained/web_manager/
├── __init__.py                 # 主模块入口
├── README.md                   # 说明文档
├── api/                        # API接口
│   ├── __init__.py
│   ├── auth.py                 # 认证接口
│   └── bot.py                  # 机器人管理接口
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── auth.py                 # 认证相关模型
│   ├── bot.py                  # 机器人相关模型
│   └── config.py               # 配置相关模型
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── auth_service.py         # 认证服务
│   └── bot_service.py          # 机器人服务
├── utils/                      # 工具类
│   ├── __init__.py
│   └── security.py             # 安全工具
├── websocket/                  # WebSocket处理
│   ├── __init__.py
│   ├── console.py              # 控制台WebSocket
│   └── chat.py                 # 聊天WebSocket
├── templates/                  # HTML模板
│   ├── base.html               # 基础模板
│   ├── index.html              # 首页
│   ├── login.html              # 登录页
│   └── dashboard.html          # 控制台页面
├── static/                     # 静态资源
│   ├── css/
│   │   └── style.css           # 样式文件
│   └── js/
│       └── common.js           # 公共JavaScript
└── static_handler.py           # 静态文件处理
```

## 开发说明

### 添加新的API接口

1. 在 `api/` 目录下创建新的路由文件
2. 在 `models/` 目录下定义相关数据模型
3. 在 `services/` 目录下实现业务逻辑
4. 在主模块中注册新的路由

### 添加新的页面

1. 在 `templates/` 目录下创建HTML模板
2. 在 `static_handler.py` 中添加路由
3. 在 `static/` 目录下添加相关的CSS和JavaScript

### WebSocket功能扩展

1. 在 `websocket/` 目录下创建新的WebSocket处理器
2. 在主模块中注册WebSocket路由
3. 在前端页面中添加WebSocket连接逻辑

## 安全注意事项

1. **修改默认密码**：首次登录后立即修改默认密码
2. **HTTPS部署**：生产环境建议使用HTTPS
3. **防火墙配置**：限制Web管理端口的访问
4. **定期更新**：保持依赖包的最新版本
5. **日志监控**：定期检查访问日志和错误日志

## 故障排除

### 常见问题

1. **Web界面无法访问**
   - 检查 `web_manager_api` 配置是否为 `true`
   - 确认端口是否被占用
   - 查看启动日志中的错误信息

2. **登录失败**
   - 确认用户名和密码是否正确
   - 检查数据库连接是否正常
   - 查看认证相关的错误日志

3. **WebSocket连接失败**
   - 确认JWT Token是否有效
   - 检查WebSocket URL是否正确
   - 查看浏览器控制台的错误信息

### 日志查看

Web管理后台的日志会输出到主程序日志中，可以通过以下方式查看：

```bash
# 查看实时日志
tail -f logs/latest.log

# 搜索Web管理相关日志
grep "Web管理" logs/latest.log
```

## 贡献指南

欢迎提交Issue和Pull Request来改进Web管理后台功能。

## 许可证

本项目遵循xiaomai-bot项目的许可证。
