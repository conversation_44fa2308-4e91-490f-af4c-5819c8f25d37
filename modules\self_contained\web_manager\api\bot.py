"""
机器人管理API接口

提供机器人实例管理、群组管理、消息发送等API
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any

from . import get_current_user, get_current_admin_user, API_PREFIX
from ..models.auth import UserInfo
from ..models.bot import (
    BotInstanceInfo, GroupInfo, FriendInfo, BotStatistics,
    BotCommand, MessageInfo
)
from ..services.bot_service import BotService

# 创建路由器
router = APIRouter(prefix=f"{API_PREFIX}/bot", tags=["机器人管理"])

# 创建服务实例
bot_service = BotService()

@router.get("/instances", response_model=List[BotInstanceInfo], summary="获取机器人实例列表")
async def get_bot_instances(current_user: UserInfo = Depends(get_current_user)) -> List[BotInstanceInfo]:
    """
    获取所有机器人实例的信息
    
    返回包含以下信息的列表：
    - 账号信息
    - 在线状态
    - 连接时间
    - 消息统计
    - 群组和好友数量
    """
    try:
        instances = await bot_service.get_bot_instances()
        return instances
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取机器人实例失败: {str(e)}"
        )

@router.get("/instances/{account}/groups", response_model=List[GroupInfo], summary="获取机器人群组列表")
async def get_bot_groups(
    account: int,
    current_user: UserInfo = Depends(get_current_user)
) -> List[GroupInfo]:
    """
    获取指定机器人的群组列表
    
    - **account**: 机器人账号
    """
    try:
        groups = await bot_service.get_bot_groups(account)
        return groups
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取群组列表失败: {str(e)}"
        )

@router.get("/instances/{account}/friends", response_model=List[FriendInfo], summary="获取机器人好友列表")
async def get_bot_friends(
    account: int,
    current_user: UserInfo = Depends(get_current_user)
) -> List[FriendInfo]:
    """
    获取指定机器人的好友列表
    
    - **account**: 机器人账号
    """
    try:
        friends = await bot_service.get_bot_friends(account)
        return friends
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取好友列表失败: {str(e)}"
        )

@router.get("/statistics", response_model=BotStatistics, summary="获取机器人统计信息")
async def get_bot_statistics(current_user: UserInfo = Depends(get_current_user)) -> BotStatistics:
    """
    获取机器人整体统计信息
    
    包括：
    - 账号数量统计
    - 群组和好友总数
    - 消息统计
    - 系统资源使用情况
    """
    try:
        statistics = await bot_service.get_bot_statistics()
        return statistics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

@router.post("/instances/{account}/restart", summary="重启机器人实例")
async def restart_bot_instance(
    account: int,
    current_user: UserInfo = Depends(get_current_admin_user)
) -> Dict[str, str]:
    """
    重启指定的机器人实例（需要管理员权限）
    
    - **account**: 机器人账号
    """
    try:
        success = await bot_service.restart_bot_instance(account)
        if success:
            return {"message": f"机器人实例 {account} 重启成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="重启失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重启机器人实例失败: {str(e)}"
        )

@router.post("/send/group", summary="发送群组消息")
async def send_group_message(
    account: int,
    group_id: int,
    message: str,
    current_user: UserInfo = Depends(get_current_admin_user)
) -> Dict[str, str]:
    """
    发送消息到指定群组（需要管理员权限）
    
    - **account**: 机器人账号
    - **group_id**: 群组ID
    - **message**: 消息内容
    """
    try:
        success = await bot_service.send_message_to_group(account, group_id, message)
        if success:
            return {"message": "消息发送成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="消息发送失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送群组消息失败: {str(e)}"
        )

@router.post("/send/friend", summary="发送好友消息")
async def send_friend_message(
    account: int,
    friend_id: int,
    message: str,
    current_user: UserInfo = Depends(get_current_admin_user)
) -> Dict[str, str]:
    """
    发送消息到指定好友（需要管理员权限）
    
    - **account**: 机器人账号
    - **friend_id**: 好友ID
    - **message**: 消息内容
    """
    try:
        success = await bot_service.send_message_to_friend(account, friend_id, message)
        if success:
            return {"message": "消息发送成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="消息发送失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送好友消息失败: {str(e)}"
        )

@router.get("/instances/{account}/status", summary="获取机器人实例状态")
async def get_bot_instance_status(
    account: int,
    current_user: UserInfo = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取指定机器人实例的详细状态信息
    
    - **account**: 机器人账号
    """
    try:
        instances = await bot_service.get_bot_instances()
        for instance in instances:
            if instance.account == account:
                return instance.dict()
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到账号为 {account} 的机器人实例"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取机器人状态失败: {str(e)}"
        )
