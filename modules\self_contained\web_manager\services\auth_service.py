"""
认证服务

处理用户认证、会话管理、权限控制等业务逻辑
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

from loguru import logger
from sqlalchemy import select

from core.config import GlobalConfig
from core.orm import orm

from ..models.auth import UserInfo, WebSession, WebUser
from ..utils.security import SecurityManager


class AuthService:
    """认证服务类"""

    def __init__(self):
        self.security = SecurityManager()

    async def initialize_default_user(self) -> None:
        """初始化默认用户"""
        try:
            # 检查是否已有用户
            existing_users = await orm.fetch_all(select(WebUser))
            if existing_users:
                return

            # 创建默认Master用户
            from creart import create

            config = create(GlobalConfig)

            default_password = "admin123"  # 固定默认密码
            password_hash = self.security.hash_password(default_password)

            master_user = WebUser(
                username="master",
                password_hash=password_hash,
                qq_id=config.Master,
                role="master",
                is_active=True,
            )

            # 使用正确的ORM方法插入数据
            await orm.add(
                WebUser.__table__,
                {
                    "username": "master",
                    "password_hash": password_hash,
                    "qq_id": config.Master,
                    "role": "master",
                    "is_active": True,
                },
            )
            logger.success(
                f"已创建默认Master用户，用户名: master，密码: {default_password}"
            )
            logger.warning("请及时修改默认密码！")

        except Exception as e:
            logger.error(f"初始化默认用户失败: {e}")

    async def authenticate_user(self, username: str, password: str) -> UserInfo | None:
        """用户认证"""
        try:
            logger.info(f"尝试认证用户: {username}")
            # 查找用户 - 明确指定字段
            user_data = await orm.fetch_one(
                select(
                    WebUser.id,
                    WebUser.username,
                    WebUser.password_hash,
                    WebUser.qq_id,
                    WebUser.role,
                    WebUser.is_active,
                    WebUser.created_at,
                    WebUser.last_login,
                    WebUser.login_count,
                ).where(WebUser.username == username, WebUser.is_active == True)
            )

            if not user_data:
                logger.warning(f"用户不存在或未激活: {username}")
                return None

            logger.info(
                f"找到用户: {username}, 数据长度: {len(user_data)}, 开始验证密码"
            )

            # 使用Row对象的索引访问
            user = WebUser(
                id=user_data[0],
                username=user_data[1],
                password_hash=user_data[2],
                qq_id=user_data[3],
                role=user_data[4],
                is_active=user_data[5],
                created_at=user_data[6],
                last_login=user_data[7],
                login_count=user_data[8],
            )

            # 验证密码
            if not self.security.verify_password(password, user.password_hash):
                return None

            # 更新登录信息
            await orm.update(
                WebUser,
                {"last_login": datetime.now(), "login_count": user.login_count + 1},
                [WebUser.id == user.id],
            )

            return UserInfo(
                id=user.id,
                username=user.username,
                qq_id=user.qq_id,
                role=user.role,
                is_active=user.is_active,
                created_at=user.created_at,
                last_login=datetime.now(),
                login_count=user.login_count + 1,
            )

        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return None

    async def create_session(
        self, user: UserInfo, ip_address: str = None, user_agent: str = None
    ) -> dict[str, Any]:
        """创建用户会话"""
        try:
            # 生成Token
            token_data = {
                "user_id": user.id,
                "username": user.username,
                "role": user.role,
            }

            expires_delta = timedelta(minutes=30)  # 30分钟过期
            access_token = self.security.create_access_token(token_data, expires_delta)

            # 生成会话ID
            session_id = self.security.generate_session_id()

            # 准备会话数据

            # 使用正确的ORM方法插入会话数据
            await orm.add(
                WebSession.__table__,
                {
                    "session_id": session_id,
                    "user_id": user.id,
                    "token": access_token,
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                    "created_at": datetime.now(),
                    "expires_at": datetime.now() + expires_delta,
                    "is_active": True,
                },
            )

            return {
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": int(expires_delta.total_seconds()),
                "session_id": session_id,
                "user_info": user.dict(),
            }

        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise

    async def verify_session(self, token: str) -> UserInfo | None:
        """验证会话"""
        try:
            # 验证Token
            payload = self.security.verify_token(token)
            if not payload:
                return None

            user_id = payload.get("user_id")
            if not user_id:
                return None

            # 检查会话是否存在且有效
            session_data = await orm.fetch_one(
                select(WebSession).where(
                    WebSession.user_id == user_id,
                    WebSession.token == token,
                    WebSession.is_active == True,
                    WebSession.expires_at > datetime.now(),
                )
            )

            if not session_data:
                return None

            # 获取用户信息
            user_data = await orm.fetch_one(
                select(
                    WebUser.id,
                    WebUser.username,
                    WebUser.password_hash,
                    WebUser.qq_id,
                    WebUser.role,
                    WebUser.is_active,
                    WebUser.created_at,
                    WebUser.last_login,
                    WebUser.login_count,
                ).where(WebUser.id == user_id, WebUser.is_active == True)
            )

            if not user_data:
                return None

            # 使用索引访问Row对象数据
            return UserInfo(
                id=user_data[0],  # id
                username=user_data[1],  # username
                qq_id=user_data[3],  # qq_id (跳过password_hash)
                role=user_data[4],  # role
                is_active=user_data[5],  # is_active
                created_at=user_data[6],  # created_at
                last_login=user_data[7],  # last_login
                login_count=user_data[8],  # login_count
            )

        except Exception as e:
            logger.error(f"验证会话失败: {e}")
            return None

    async def logout(self, token: str) -> bool:
        """用户登出"""
        try:
            await orm.update(
                WebSession, {"is_active": False}, [WebSession.token == token]
            )
            return True
        except Exception as e:
            logger.error(f"用户登出失败: {e}")
            return False

    async def change_password(
        self, user_id: int, old_password: str, new_password: str
    ) -> bool:
        """修改密码"""
        try:
            # 获取用户
            user_data = await orm.fetch_one(
                select(WebUser).where(WebUser.id == user_id)
            )

            if not user_data:
                return False

            user = WebUser(**dict(user_data))

            # 验证旧密码
            if not self.security.verify_password(old_password, user.password_hash):
                return False

            # 更新密码
            new_password_hash = self.security.hash_password(new_password)
            await orm.update(
                WebUser, {"password_hash": new_password_hash}, [WebUser.id == user_id]
            )

            # 使所有会话失效
            await orm.update(
                WebSession, {"is_active": False}, [WebSession.user_id == user_id]
            )

            return True

        except Exception as e:
            logger.error(f"修改密码失败: {e}")
            return False


# 创建全局认证服务实例
auth_service = AuthService()
