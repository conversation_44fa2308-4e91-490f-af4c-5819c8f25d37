"""
Web管理后台数据模型

定义Web管理系统所需的数据模型，包括认证、配置等
"""

from .auth import WebUser, WebSession, LoginRequest, TokenResponse
from .bot import BotInstanceInfo, BotStatus, BotCommand
from .config import ConfigItem, ModuleConfig, SystemConfig

__all__ = [
    "WebUser",
    "WebSession", 
    "LoginRequest",
    "TokenResponse",
    "BotInstanceInfo",
    "BotStatus",
    "BotCommand",
    "ConfigItem",
    "ModuleConfig", 
    "SystemConfig"
]
