import datetime
from pathlib import Path

import jieba
from graia.ariadne.event.message import Group, GroupMessage, Member
from graia.ariadne.message.chain import MessageChain
from graia.ariadne.message.element import Plain
from graia.ariadne.util.saya import decorate, listen
from graia.saya import Channel, Saya

from core.control import Distribute
from core.models import saya_model
from core.orm import orm
from core.orm.tables import ChatRecord

# 关闭 jieba 的 Debug log
jieba.setLogLevel(jieba.logging.INFO)
module_controller = saya_model.get_module_controller()
saya = Saya.current()
channel = Channel.current()
channel.meta["name"] = "ChatRecorder"
channel.meta["author"] = "SAGIRI-kawaii"
channel.meta["description"] = "一个记录聊天记录的插件，可配合词云等插件使用"
channel.metadata = module_controller.get_metadata_from_path(Path(__file__))


async def _broadcast_new_message(message: MessageChain, group: Group, member: Member):
    """向Web管理界面广播新消息"""
    try:
        # 动态导入，避免循环依赖
        from modules.self_contained.web_manager.websocket.chat import chat_ws
        from utils.image import get_user_avatar_url

        # 获取消息内容
        content = message.display.strip()
        if not content:
            return

        # 检查是否为机器人消息
        is_bot = False
        sender_name = member.name or f"用户{member.id}"
        try:
            from core.config import GlobalConfig

            config = GlobalConfig()
            bot_accounts = {int(account["account"]) for account in config.bot_accounts}
            if member.id in bot_accounts:
                is_bot = True
                # 尝试获取机器人的真实昵称
                try:
                    from graia.ariadne import Ariadne

                    if member.id in Ariadne.service.connections:
                        app = Ariadne.current(member.id)
                        if app.connection.status.available:
                            bot_profile = await app.get_bot_profile()
                            if bot_profile.nickname:
                                sender_name = bot_profile.nickname
                except Exception:
                    sender_name = f"小埋({member.id})"
        except Exception:
            pass

        # 获取用户头像
        sender_avatar = ""
        try:
            sender_avatar = await get_user_avatar_url(member.id)
        except Exception:
            pass

        # 构造消息数据
        message_data = {
            "type": "new_message",
            "group_id": str(group.id),
            "message": {
                "id": f"msg_{datetime.datetime.now().timestamp()}",
                "sender_name": sender_name,
                "content": content,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "message_type": "text",
                "sender_avatar": sender_avatar,
            },
        }

        # 广播到订阅该群组的WebSocket连接
        await chat_ws.broadcast_to_group(str(group.id), message_data)

    except Exception:
        # 静默处理错误，不影响正常的消息记录功能
        pass


@listen(GroupMessage)
@decorate(Distribute.require())
async def chat_record(message: MessageChain, group: Group, member: Member):
    content = "".join(plain.text for plain in message.get(Plain)).strip()
    seg_result = jieba.lcut(content) if content else ""

    # 记录到数据库
    await orm.add(
        table=ChatRecord,
        data={
            "time": datetime.datetime.now(),
            "group_id": group.id,
            "member_id": member.id,
            "persistent_string": message.as_persistent_string(),
            "seg": "|".join(seg_result) if seg_result else "",
        },
    )

    # 实时推送到Web管理界面
    await _broadcast_new_message(message, group, member)
